import { OpenapiValidatorModule } from '@mynotary/backend/shared/openapi-validator-infra';
import { Module } from '@nestjs/common';
import { join } from 'path';
import { PdfController } from '@mynotary/backend/pdf/feature';
import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { provideSecretsScope } from '@mynotary/backend/secrets/providers';
import { ArchivesController, FilesController } from '@mynotary/backend/files/feature';
import { provideAuthenticationsScope } from '@mynotary/backend/authentications/providers';
import { provideAuthorizationsScope } from '@mynotary/backend/authorizations/providers';
import { provideEmailsScope } from '@mynotary/backend/emails/providers';
import { provideAsyncTasksScope } from '@mynotary/backend/async-tasks/providers';
import { provideThemesScope } from '@mynotary/backend/themes/providers';
import { provideFeatureOrganizationsScope } from '@mynotary/backend/feature-organizations/providers';
import { provideOrganizationHoldingsScope } from '@mynotary/backend/organization-holdings/providers';
import { providePdfScope } from '@mynotary/backend/pdf/providers';
import { provideFilesScope } from '@mynotary/backend/files/providers';

@Module({
  controllers: [PdfController, FilesController, ArchivesController],
  imports: [
    OpenapiValidatorModule.forRoot({
      apiSpecPath: join(__dirname, 'assets/api-files.openapi.yaml')
    })
  ],
  providers: [
    ...provideAsyncTasksScope(),
    ...provideAuthenticationsScope(),
    ...provideAuthorizationsScope(),
    ...provideEmailsScope(),
    ...provideFeatureOrganizationsScope(),
    ...provideFilesScope(),
    ...provideOrganizationHoldingsScope(),
    ...providePdfScope(),
    ...provideSecretsScope(),
    ...provideThemesScope(),
    PrismaService
  ]
})
export class AppModule {}
