import * as ReactDOM from 'react-dom/client';

import App from 'app/app/app';
import { Provider } from 'react-redux';
import { store, storeDispatch } from '@mynotary/frontend/front-mynotary/store';
import UnsupportedBrowser from 'pages/public/unsuportedPage/unsupportedBrowser';
import { globalAxiosConfig } from '@mynotary/frontend/shared/axios-util';
import { setHttpError } from '@mynotary/frontend/snackbars/store';

import { provideArchivesInfra } from '@mynotary/frontend/archives/infra';
import { provideAuthInfra } from '@mynotary/frontend/auth/infra';
import { provideBillingsInfra } from '@mynotary/frontend/billings/infra';
import { provideCompaniesInfra } from '@mynotary/frontend/companies/infra';
import { provideContractsInfra } from '@mynotary/frontend/contract-validators/infra';
import { provideCustomViewsInfra } from '@mynotary/frontend/custom-views/infra';
import { provideCustomerSupportInfra } from '@mynotary/frontend/customer-support/infra';
import { provideExternalAppsInfra } from '@mynotary/frontend/external-apps/infra';
import { provideFeaturesInfra } from '@mynotary/frontend/features/infra';
import { provideFilesInfra } from '@mynotary/frontend/files/infra';
import { provideLegalComponentsInfra } from '@mynotary/frontend/legals/infra';
import { provideMembersInfra } from '@mynotary/frontend/members/infra';
import { provideOperationAccessInfra } from '@mynotary/frontend/operation-access/infra';
import { provideOperationDefaultAnswersInfra } from '@mynotary/frontend/legals/infra';
import { provideOrganizationDataTransfersInfra } from '@mynotary/frontend/organization-data-transfers/infra';
import { provideOperationViewsInfra } from '@mynotary/frontend/operation-views/infra';
import { provideOrganizationsInfra } from '@mynotary/frontend/organizations/infra';
import { providePdfInfra } from '@mynotary/frontend/pdf/infra';
import { provideRegisteredLettersInfra } from '@mynotary/frontend/registered-letters/infra';
import { provideRegistersInfra } from '@mynotary/frontend/registers/infra';
import { provideRolesInfra } from '@mynotary/frontend/roles/infra';
import { provideSignaturesInfra } from '@mynotary/frontend/signatures/infra';
import { provideTableauInfra } from '@mynotary/frontend/data-tableau/infra';
import { provideThemesInfra } from '@mynotary/frontend/themes/infra';
import { provideUnisInfra } from '@mynotary/frontend/unis/infra';
import { provideUsersInfra } from '@mynotary/frontend/users/infra';
import { provideContractViewsInfra } from '@mynotary/frontend/contract-views/infra';
import { provideDocumentRequestsInfra } from '@mynotary/frontend/document-requests/infra';
import { provideDrivesInfra } from '@mynotary/frontend/drives/infra';
import { provideNotificationsInfra } from '@mynotary/frontend/notifications/infra';
import { provideInvoicesInfra } from '@mynotary/frontend/invoices/infra';
import { provideLegalTemplatesInfra } from '@mynotary/frontend/legal-templates/infra';
import { provideLearnWorldsInfra } from '@mynotary/frontend/learn-worlds/infra';
import { provideOperationInvitationsInfra } from '@mynotary/frontend/operation-invitations/infra';
import { provideCsvInfra } from '@mynotary/frontend/csv/infra';
import { provideProgramsInfra } from '@mynotary/frontend/programs/infra';
import { provideCoproprieteInfra } from '@mynotary/frontend/coproprietes/infra';
import { provideLegalRecordExportsInfra } from '@mynotary/frontend/legal-record-exports/infra';
import { provideOrganizationSetupsInfra } from '@mynotary/frontend/organization-setups/infra';
import { provideOperationDefaultRecordsInfra } from '@mynotary/frontend/operation-default-records/infra';
import { provideGelAvoirsInfra } from '@mynotary/frontend/gel-avoirs/infra';
import { isValidBrowser } from '@mynotary/frontend/shared/util';
import { providePlacesInfra } from '@mynotary/frontend/places/infra';
import { provideOrdersInfra } from '@mynotary/frontend/orders/infra';
import { provideLegalOperationFetchInfra } from '@mynotary/frontend/legal-operations-fetch/infra';
import { provideEmailsInfra } from '@mynotary/frontend/emails/infra';
import { provideContractValidationsInfra } from '@mynotary/frontend/contract-validations/infra';
import { provideContractReviewsInfra } from '@mynotary/frontend/contract-reviews/infra';

import { LocalStorageForbidden } from 'pages/public/local-storage-forbidden/local-storage-forbidden';
import { NX_PUBLIC_FRONT_MYNOTARY_VERSION } from '@mynotary/frontend/shared/environments-util';
import { Maintenance } from 'pages/maintenance/maintenance';
import React from 'react';
import { logout } from '@mynotary/frontend/auth/store';

provideArchivesInfra();
provideAuthInfra();
provideBillingsInfra();
provideCompaniesInfra();
provideContractsInfra();
provideContractValidationsInfra();
provideContractReviewsInfra();
provideCustomViewsInfra();
provideCustomerSupportInfra();
provideDrivesInfra();
provideExternalAppsInfra();
provideFeaturesInfra();
provideFilesInfra();
provideLegalComponentsInfra();
provideMembersInfra();
provideOperationAccessInfra();
provideOperationDefaultAnswersInfra();
provideOperationDefaultRecordsInfra();
provideOperationViewsInfra();
provideOrganizationDataTransfersInfra();
provideOrganizationsInfra();
provideProgramsInfra();
providePdfInfra();
provideRegisteredLettersInfra();
provideRegistersInfra();
provideRolesInfra();
provideSignaturesInfra();
provideTableauInfra();
provideThemesInfra();
provideUnisInfra();
provideUsersInfra();
provideContractViewsInfra();
provideDocumentRequestsInfra();
provideNotificationsInfra();
provideInvoicesInfra();
provideLearnWorldsInfra();
provideLegalTemplatesInfra();
provideOperationInvitationsInfra();
provideCsvInfra();
provideCoproprieteInfra();
provideLegalRecordExportsInfra();
provideOrganizationSetupsInfra();
provideGelAvoirsInfra();
providePlacesInfra();
provideOrdersInfra();
provideLegalOperationFetchInfra();
provideEmailsInfra();

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);

globalAxiosConfig.onError((error) => {
  storeDispatch(setHttpError(error));
});

/*
 * Watch 401s and update RTK store which is the single source of truth.
 */
globalAxiosConfig.onUnauthorized(() => {
  storeDispatch(logout());
});

const hasLocalStorage = () => {
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
    return true;
  } catch (e) {
    console.log(e);
    return false;
  }
};

if (!hasLocalStorage()) {
  root.render(<LocalStorageForbidden />);
} else if (!isValidBrowser) {
  root.render(<UnsupportedBrowser />);
} else if (NX_PUBLIC_FRONT_MYNOTARY_VERSION === 'MAINTENANCE') {
  root.render(
    <Provider store={store}>
      <Maintenance />
    </Provider>
  );
} else {
  root.render(
    <Provider store={store}>
      <App />
    </Provider>
  );
}
