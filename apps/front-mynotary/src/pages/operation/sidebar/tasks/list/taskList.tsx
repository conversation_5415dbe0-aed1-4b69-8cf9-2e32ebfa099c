import './taskList.scss';
import React, { ReactElement, useState } from 'react';
import { MnConfirmationPopin } from '@mynotary/frontend/shared/ui';
import { MnTaskTile } from '../taskTile/taskTile';
import { Task, Operation, Record } from '@mynotary/frontend/legals/core';
import { MnProps, classNames } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { deleteTask } from '@mynotary/frontend/legals/store';

interface TaskListProps extends MnProps {
  legalComponent: Record | Operation;
  tasks: Task[];
}

const MnTaskList = ({ className, legalComponent, tasks }: TaskListProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const [toDeleteTask, setToDeleteTask] = useState<Task>();

  const handleDeleteTask = (): void => {
    if (toDeleteTask) {
      dispatch(deleteTask(legalComponent.id, toDeleteTask?.id)).then(() => {
        setToDeleteTask(undefined);
      });
    }
  };

  return (
    <>
      {tasks.length === 0 && (
        <div className='tl-empty'>
          Aucune tâche ne vous a été assignée et vous n’en avez attribuée à personne pour le moment.
        </div>
      )}
      <div className={classNames('task-list', className)}>
        {tasks.map((task) => (
          <div className='tl-task-container' key={task.id}>
            <MnTaskTile onDelete={() => setToDeleteTask(task)} task={task} />
          </div>
        ))}
        {toDeleteTask && (
          <MnConfirmationPopin
            cancel='Annuler'
            content='Supprimer cette tâche définitivement ?'
            onCancel={() => setToDeleteTask(undefined)}
            onValidate={handleDeleteTask}
            validate='Confirmer'
          />
        )}
      </div>
    </>
  );
};

export { MnTaskList };
