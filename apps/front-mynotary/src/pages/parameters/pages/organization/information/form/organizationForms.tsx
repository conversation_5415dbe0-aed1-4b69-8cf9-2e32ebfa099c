import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { keys, size, split } from 'lodash';
import { Organization } from '@mynotary/frontend/organizations/core';
import { OrganizationType } from '@mynotary/crossplatform/organizations/api';
import { Subscription } from '@mynotary/frontend/billings/core';
import { FormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { getOrganizationFormByType } from '@mynotary/frontend/organizations/feature';

export const mapAnswerToOrganization = (answer: AnswerDict): Partial<Organization> => {
  return {
    address: answer['address']?.value,
    email: answer['email']?.value,
    hubspotId: answer['hubspotId']?.value,
    name: answer['name']?.value,
    rib: size(answer['rib']?.value) ? keys(answer['rib'].value)[0] : undefined
  };
};

export const mapOrganizationToAnswer = (orga?: Organization): AnswerDict => {
  if (orga) {
    const [uniqId] = split(orga.uniqueIdentifier, '$$');
    return {
      address: {
        value: orga.address
      },
      crpcen: { value: uniqId },
      email: { value: orga.email },
      hubspotId: { value: orga.hubspotId },
      name: { value: orga.name },
      rib: { value: orga.rib ? { [orga.rib]: 1 } : {} },
      siren: { value: uniqId },
      uniqueIdentifier: { value: orga.uniqueIdentifier },
      zip: { value: orga?.address?.zip }
    };
  }
  return {};
};

export const mapSubscriptionToCustomerAnswer = (subscription: Subscription): AnswerDict => {
  return {
    address: { value: subscription.customer.address },
    billingEmail: { value: subscription.customer.email },
    customerCivility: { value: subscription.customer.civility },
    customerFirstname: { value: subscription.customer.firstname },
    customerLastname: { value: subscription.customer.lastname },
    name: { value: subscription.customer.organizationName },
    registrationNumber: { value: subscription.customer.siren }
  };
};

export const getAdminOrganizationsForm = (type?: OrganizationType): FormQuestion[] => {
  return [
    ...getOrganizationFormByType(type),
    {
      id: 'hubspotId',
      label: 'Id hubspot',
      optional: true,
      type: 'NUMBER'
    }
  ];
};
