import { Injectable } from '@nestjs/common';
import { DriveFolder } from './drives';
import { CreateDriveFolderArgs, DriveFoldersRepository, UpdateDriveFolderArgs } from './drive-folders.repository';
import { DriveFilesRepository } from './drive-files.repository';

@Injectable()
export class DriveFoldersService {
  constructor(
    private driveFoldersRepository: DriveFoldersRepository,
    private driveFileRepository: DriveFilesRepository
  ) {}

  async createDriveFolder(args: CreateDriveFolderArgs): Promise<DriveFolder> {
    return await this.driveFoldersRepository.createDriveFolder(args);
  }

  async getDriveFolders(operationId: string) {
    return await this.driveFoldersRepository.getDriveFolders({ operationId: operationId });
  }

  async deleteDriveFolder(id: string): Promise<void> {
    const driveFiles = await this.driveFileRepository.getDriveFiles({ folderId: id });

    for (const driveFile of driveFiles) {
      await this.driveFileRepository.deleteDriveFile({ driveFileId: driveFile.id });
    }

    return await this.driveFoldersRepository.deleteDriveFolder(id);
  }

  async updateDriveFolder(args: UpdateDriveFolderArgs): Promise<void> {
    await this.driveFoldersRepository.updateDriveFolder(args);
  }
}
