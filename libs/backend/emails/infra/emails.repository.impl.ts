import {
  Attachment,
  Branding,
  CreateEmailArgs,
  CreateEmailLogArgs,
  Email,
  EmailData,
  EmailNewData,
  EmailsRepository,
  EmailWithLogs,
  FindEmailArgs,
  GetEmailArgs,
  getEmailsByCategory,
  getSenderFromEmailData,
  SenderInfo,
  UpdateEmailArgs
} from '@mynotary/backend/emails/core';
import { Injectable } from '@nestjs/common';
import { Prisma, PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { EmailsTheme } from '@mynotary/crossplatform/themes/api';
import { EnvService } from '@mynotary/backend/secrets/api';
import { convertEnum, Exception, JSONObject } from '@mynotary/crossplatform/shared/util';

import { EmailEventType, EmailTemplateId } from '@mynotary/crossplatform/emails/core';
import JsonValue = Prisma.JsonValue;

@Injectable()
export class EmailsRepositoryImpl implements EmailsRepository {
  constructor(
    private prismaService: PrismaService,
    private envService: EnvService
  ) {}

  async findEmail({ id, providerId }: FindEmailArgs): Promise<Email | null> {
    if (id == null && providerId == null) {
      throw new Exception("Either 'id' or 'providerId' must be provided");
    }

    const email: SelectedEmail | null = await this.prismaService.email.findUnique({
      select: SELECTED_EMAIL_FIELDS,
      where: id != null ? { id: parseInt(id) } : { provider_id: providerId }
    });

    return email != null ? convertDbToEmail(email) : null;
  }

  async createEmailLog(args: CreateEmailLogArgs): Promise<{ id: string }> {
    const emailLog = await this.prismaService.email_log.create({
      data: {
        creation_time: new Date(args.creationTime),
        email_id: parseInt(args.emailId),
        status: args.status
      },
      select: {
        id: true
      }
    });

    return { id: emailLog.id.toString() };
  }

  /**
   * Retrieves a list of emails with their logs based on the provided arguments.
   *
   * The algorithm works as follows:
   * 1. Fetches emails from the database, ordered by creation time in descending order.
   * 2. Filters emails based on the provided arguments: `invoiceId`, `operationId`, `search`, and `category`.
   * 3. Converts the fetched emails to the `EmailWithLogs` format.
   * 4. Identifies emails of type `OPERATION_INVITATION` and fetches corresponding user details.
   *
   * @param {GetEmailArgs} args - The arguments to filter the emails.
   * @param {string} [args.invoiceId] - The ID of the invoice to filter emails by.
   * @param {string} [args.operationId] - The ID of the operation to filter emails by.
   * @param {string} [args.search] - A search string to filter emails by receiver.
   * @param {string} [args.category] - The category to filter emails by.
   * @returns {Promise<EmailWithLogs[]>} A promise that resolves to an array of emails with their logs.
   */
  async getEmails(args: GetEmailArgs): Promise<EmailWithLogs[]> {
    const emails: SelectedEmailWithLogs[] = await this.prismaService.email.findMany({
      orderBy: { creation_time: 'desc' },
      select: SELECTED_EMAIL_WITH_LOGS,
      take: 100,
      where: {
        invoice_id: args.invoiceId != null ? parseInt(args.invoiceId) : undefined,
        operation_id: args.operationId != null ? parseInt(args.operationId) : undefined,
        receiver: args.search != null ? { contains: args.search } : undefined,
        type: args.category != null ? { in: getEmailsByCategory(args.category) } : undefined
      }
    });

    const emailWithLogs = emails.map(convertDbToEmailWithLogs);

    const emailReceivers = emailWithLogs
      .filter((email) => email.type === EmailTemplateId.OPERATION_INVITATION)
      .map((email) => {
        return {
          email: email.receiver,
          id: email.id
        };
      });

    const users = await this.prismaService.user.findMany({
      select: {
        email: true,
        firstname: true,
        lastname: true
      },
      where: {
        email: {
          in: emailReceivers.map((receiver) => receiver.email)
        }
      }
    });

    return emailWithLogs.map((email) => {
      if (email.type !== EmailTemplateId.OPERATION_INVITATION) {
        return email;
      }

      const emailReceiver = emailReceivers.find((receiver) => receiver.id === email.id);
      if (emailReceiver) {
        const user = users.find((user) => user.email === email.receiver);

        return {
          ...email,
          subtitle: user != null ? `${user.firstname} ${user.lastname}` : 'Intervenant externe'
        };
      }

      return email;
    });
  }

  async updateEmail(args: UpdateEmailArgs): Promise<void> {
    await this.prismaService.email.update({ data: { provider_id: args.providerId }, where: { id: parseInt(args.id) } });
  }

  async createEmail({ email, theme }: CreateEmailArgs): Promise<{ id: string }> {
    const attachments = convertToAttachementParams(email.data);
    const branding = this.convertToThemeParams(theme);

    const emailData = {
      contractId: 'contractId' in email.data ? email.data.contractId : undefined,
      data: {
        ...email.data,
        ...branding,
        ...attachments
      },
      invoiceId: 'invoiceId' in email.data ? email.data.invoiceId : undefined,
      operationId: 'operationId' in email.data ? email.data.operationId : undefined,
      receiver: email.receiver,
      taskId: 'taskId' in email.data ? email.data.taskId : undefined,
      type: email.data.templateId
    } satisfies EmailNewDb;

    const emailCreated = await this.prismaService.email.create({
      data: {
        contract_id: emailData.contractId != null ? parseInt(emailData.contractId) : undefined,
        data: emailData.data as JSONObject,
        invoice_id: emailData.invoiceId != null ? parseInt(emailData.invoiceId) : undefined,
        operation_id: emailData.operationId != null ? parseInt(emailData.operationId) : undefined,
        receiver: email.receiver,
        task_id: emailData.taskId != null ? parseInt(emailData.taskId) : undefined,
        type: emailData.type
      },
      select: {
        id: true
      }
    });

    return { id: emailCreated.id.toString() };
  }

  private convertToThemeParams(theme: EmailsTheme | null) {
    if (theme == null) {
      return null;
    }

    return {
      branding: {
        color: theme.mainColor,
        logo: this.getPublicFile(theme.logo),
        url: this.envService.url.mnAppUrl
      }
    };
  }

  private getPublicFile = (id: string) => {
    return `${this.envService.url.apiJavaUrl}/files/${id}`;
  };
}

function convertDbToEmail(email: SelectedEmail): Email {
  return {
    creationTime: email.creation_time.toISOString(),
    data: convertDbEmailDataToEmailData(email.data),
    id: email.id.toString(),
    invoiceId: email.invoice_id != null ? email.invoice_id.toString() : undefined,
    operationId: email.operation_id != null ? email.operation_id.toString() : undefined,
    providerId: email.provider_id != null ? email.provider_id.toString() : undefined,
    receiver: email.receiver,
    type: convertEnum(EmailTemplateId, email.type)
  };
}

function convertDbEmailDataToEmailData(data: JsonValue): EmailData {
  return data as unknown as EmailData;
}

function convertDbToEmailWithLogs(dbEmailWithLog: SelectedEmailWithLogs): EmailWithLogs {
  const data = convertDbEmailDataToEmailData(dbEmailWithLog.data);
  return {
    creationTime: dbEmailWithLog.creation_time.toISOString(),
    data,
    id: dbEmailWithLog.id.toString(),
    invoiceId: dbEmailWithLog.invoice_id != null ? dbEmailWithLog.invoice_id.toString() : undefined,
    logs: dbEmailWithLog.email_logs.map((log) => ({
      creationTime: log.creation_time.toISOString(),
      status: convertEnum(EmailEventType, log.status)
    })),
    operationId: dbEmailWithLog.operation_id != null ? dbEmailWithLog.operation_id.toString() : undefined,
    providerId: dbEmailWithLog.provider_id != null ? dbEmailWithLog.provider_id.toString() : undefined,
    receiver: dbEmailWithLog.receiver,
    sender: getSenderFromEmailData(data)?.email,
    subtitle: convertAdditionalInfoDbToSubtitle(dbEmailWithLog) ?? undefined,
    type: convertEnum(EmailTemplateId, dbEmailWithLog.type)
  };
}

function convertAdditionalInfoDbToSubtitle(dbEmailWithLog: SelectedEmailWithLogs): string | null {
  const type = convertEnum(EmailTemplateId, dbEmailWithLog.type);

  switch (type) {
    case EmailTemplateId.SIGNATURE_COMPLETED:
    case EmailTemplateId.SIGNATURE_DEADLINE:
    case EmailTemplateId.SIGNATURE_SIGNATORY_SIGNED:
    case EmailTemplateId.SIGNATURE_REQUEST:
    case EmailTemplateId.SIGNATURE_REQUEST_RESEND:
    case EmailTemplateId.CONTRACT_VALIDATION:
    case EmailTemplateId.REGISTER_LETTER_COMPLETED:
    case EmailTemplateId.REGISTER_LETTER_COMPLETED_SHARED:
    case EmailTemplateId.REGISTER_LETTER_ACCEPTED:
    case EmailTemplateId.REGISTER_LETTER_NEGLIGENCE:
    case EmailTemplateId.REGISTER_LETTER_REFUSED:
    case EmailTemplateId.REGISTER_LETTER_ERROR:
    case EmailTemplateId.TASK_READ_PROJECT_CONTRACT:
    case EmailTemplateId.TASK_VALIDATE_CONTRACT:
    case EmailTemplateId.TASK_REVIEW_CONTRACT:
      return dbEmailWithLog.operation_contract?.label ?? null;
    case EmailTemplateId.TASK_CUSTOM:
      return dbEmailWithLog.task?.title ?? null;
    case EmailTemplateId.ACCOUNT_CREATION_CODE:
    case EmailTemplateId.INVOICE_CREATION:
    case EmailTemplateId.LEGAL_RECORD_EXPORT_COMPLETED:
    case EmailTemplateId.MANDAT_AR24:
    case EmailTemplateId.OPERATION_INVITATION:
    case EmailTemplateId.ORDER_COMPLETED:
    case EmailTemplateId.ORDER_PAYMENT_REQUEST:
    case EmailTemplateId.ORGANIZATION_INVITATION:
    case EmailTemplateId.ORGANIZATION_INVITATION_MYNOTARY:
    case EmailTemplateId.PASSWORD_RECOVERY:
    case EmailTemplateId.SIGNATURE_ERROR_POST_ACTIVATION:
    case EmailTemplateId.TASK_DOCUMENT_REQUEST:
    case EmailTemplateId.TASK_DOWNLOAD_FILES:
    case EmailTemplateId.TASK_DOWNLOAD_FILES_OPERATION:
    case EmailTemplateId.TASK_FILL_OPERATION_RECORDS:
    case EmailTemplateId.TASK_REMINDER:
    case EmailTemplateId.TASK_SHARE_OPERATION_RECORD:
    case EmailTemplateId.TASK_RESEND:
      return null;
  }
}

function convertToAttachementParams(args: EmailNewData): { attachments: Attachment[] } | undefined {
  switch (args.templateId) {
    case EmailTemplateId.INVOICE_CREATION: {
      const content = args.invoiceBuffer.toString('base64');
      return {
        attachments: [{ content, filename: `${args.invoiceLabel}.pdf` }]
      };
    }
  }
  return undefined;
}

export function convertSenderInfoToParams(sender: SenderInfo) {
  return {
    email: sender.email,
    firstname: sender.firstname,
    lastname: sender.lastname,
    organizationName: sender.organizationName,
    phone: sender.phone
  };
}

const SELECTED_EMAIL_WITH_LOGS = Prisma.validator<Prisma.emailSelect>()({
  creation_time: true,
  data: true,
  email_logs: {
    select: {
      creation_time: true,
      status: true
    }
  },
  id: true,
  invoice_id: true,
  operation_contract: {
    select: {
      label: true
    }
  },
  operation_id: true,
  provider_id: true,
  receiver: true,
  task: {
    select: {
      title: true
    }
  },
  type: true
});

const SELECTED_EMAIL_FIELDS = Prisma.validator<Prisma.emailSelect>()({
  creation_time: true,
  data: true,
  id: true,
  invoice_id: true,
  operation_id: true,
  provider_id: true,
  receiver: true,
  type: true
});

type SelectedEmailWithLogs = Prisma.emailGetPayload<{ select: typeof SELECTED_EMAIL_WITH_LOGS }>;

type SelectedEmail = Prisma.emailGetPayload<{ select: typeof SELECTED_EMAIL_FIELDS }>;

export interface EmailNewDb {
  contractId?: string;
  data: EmailNewData & Branding & { attachments?: Attachment[] };
  invoiceId?: string;
  operationId?: string;
  receiver: string;
  taskId?: string;
  type: EmailTemplateId;
}
