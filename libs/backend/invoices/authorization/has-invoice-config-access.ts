import { Injectable, Type, UnprocessableEntityException } from '@nestjs/common';
import { AuthorizationArgs, bodyResolver } from '@mynotary/backend/shared/auth-util';
import { AuthorizationBase, AuthorizationsApiService } from '@mynotary/backend/authorizations/api';

interface HasInvoiceConfigAccessOption {
  invoiceConfigIdResolver?: (args: AuthorizationArgs) => string | null;
}
export function HasInvoiceConfigAccess(options: HasInvoiceConfigAccessOption = {}): Type<AuthorizationBase> {
  const invoiceConfigIdResolver = options?.invoiceConfigIdResolver ?? bodyResolver('invoiceConfigId');

  @Injectable()
  class HasInvoiceConfigAccess extends AuthorizationBase {
    constructor(private authorizationsApiService: AuthorizationsApiService) {
      super();
    }

    async isAuthorized(authorizationRequest: AuthorizationArgs) {
      const userId = authorizationRequest.userInfo?.userId;
      const invoiceConfigId = invoiceConfigIdResolver(authorizationRequest);

      const isUserVerified = await this.authorizationsApiService.isUserVerified(userId);

      if (!isUserVerified || userId == null) {
        return false;
      }

      if (invoiceConfigId == null) {
        throw new UnprocessableEntityException('Invoice config id is required');
      }

      return this.authorizationsApiService.hasInvoiceConfigAccess({ invoiceConfigId, userId });
    }
  }

  return HasInvoiceConfigAccess;
}
