import { provideInvoicesScope } from '@mynotary/backend/invoices/providers';
import { provideEmailsTest } from '@mynotary/backend/emails/test';
import { provideFeatureOrganizationsTest } from '@mynotary/backend/feature-organizations/test';
import { provideUsersTest } from '@mynotary/backend/users/test';
import { provideOrganizationsTest } from '@mynotary/backend/organizations/test';
import { provideThemesTest } from '@mynotary/backend/themes/test';
import { providePdfClientTest } from '@mynotary/backend/pdf-client/test';
import { provideAuthorizationsTest } from '@mynotary/backend/authorizations/test';

export const provideInvoicesTest = () => {
  return [
    ...provideAuthorizationsTest(),
    ...provideEmailsTest(),
    ...provideFeatureOrganizationsTest(),
    ...provideInvoicesScope(),
    ...provideOrganizationsTest(),
    ...providePdfClientTest(),
    ...provideThemesTest(),
    ...provideUsersTest()
  ];
};
