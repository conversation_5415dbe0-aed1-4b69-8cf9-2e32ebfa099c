import { Injectable, Type, UnprocessableEntityException } from '@nestjs/common';
import { AuthorizationArgs, pathResolver } from '@mynotary/backend/shared/auth-util';
import { AuthorizationBase } from '@mynotary/backend/authorizations/api';
import { AuthorizationsApiService } from '@mynotary/backend/authorizations/api';
import { LegalLinkAuthorizationsRepository } from '@mynotary/backend/legals/core';

interface HasLegalLinkAccessArgs {
  linkIdResolver?: (args: AuthorizationArgs) => string | null;
}

/**
 * Access is granted if:
 * - User is the creator of the legal link
 * - User has permission to update legal records at organization level
 */
export function HasLegalLinkAccess(args?: HasLegalLinkAccessArgs): Type<AuthorizationBase> {
  const linkIdResolver = args?.linkIdResolver ?? pathResolver('linkId');

  @Injectable()
  class HasLegalLinkAccess extends AuthorizationBase {
    constructor(
      private legalLinkAuthorizationsRepository: LegalLinkAuthorizationsRepository,
      private authorizationsApiService: AuthorizationsApiService
    ) {
      super();
    }

    async isAuthorized(authorizationRequest: AuthorizationArgs) {
      const userId = authorizationRequest.userInfo?.userId;
      const legalLinkId = linkIdResolver(authorizationRequest);
      const isUserVerified = await this.authorizationsApiService.isUserVerified(userId);

      if (userId == null || !isUserVerified) {
        return false;
      }

      if (legalLinkId == null) {
        throw new UnprocessableEntityException('linkId is required');
      }

      return await this.legalLinkAuthorizationsRepository.hasLegalLinkAccess({ legalLinkId, userId });
    }
  }

  return HasLegalLinkAccess;
}
