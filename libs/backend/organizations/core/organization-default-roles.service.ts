import { RolesApiService } from '@mynotary/backend/roles/api';
import { Injectable } from '@nestjs/common';
import { OrganizationsRepository } from './organizations.repository';
import {
  ADMIN_ROLE_NAME,
  NOTAIRE_ROLE_NAME,
  COLLABORATEUR_ROLE_NAME,
  RESPONSABLE_ROLE_NAME,
  EntityType,
  CLERC_ROLE_NAME,
  FORMALISTE_ROLE_NAME
} from '@mynotary/crossplatform/roles/api';
import { OrganizationType } from '@mynotary/crossplatform/organizations/api';

@Injectable()
export class OrganizationDefaultRolesService {
  constructor(
    private roleApiService: RolesApiService,
    private organizationRepository: OrganizationsRepository
  ) {}

  async createOrganizationDefaultRoles(organization: {
    id: string;
    type: OrganizationType;
  }): Promise<{ adminRoleId: string }> {
    const { id: organizationId, type: organizationType } = organization;

    const adminRole = await this.roleApiService.createRole({ name: ADMIN_ROLE_NAME, organizationId });
    const collabRole = await this.roleApiService.createRole({ name: COLLABORATEUR_ROLE_NAME, organizationId });
    await this.roleApiService.createRole({ name: RESPONSABLE_ROLE_NAME, organizationId });
    await this.roleApiService.createRole({ name: NOTAIRE_ROLE_NAME, organizationId });
    if (organization.type === OrganizationType.PORTALYS_NOTARY_OFFICE) {
      await this.roleApiService.createRole({ name: CLERC_ROLE_NAME, organizationId });
      await this.roleApiService.createRole({ name: FORMALISTE_ROLE_NAME, organizationId });
    }

    /**
     * Generate default permissions for each default role
     * By default, we generate permissions for the following entities:
     * - Organization
     * - Document
     * - Data transfert
     */
    await this.roleApiService.generateDefaultRolesPermissions({
      entityType: EntityType.ORGANIZATION,
      organizationId,
      organizationType
    });
    await this.roleApiService.generateDefaultRolesPermissions({
      entityType: EntityType.DOCUMENT,
      organizationId,
      organizationType
    });
    await this.roleApiService.generateDefaultRolesPermissions({
      entityType: EntityType.DATA_TRANSFERT,
      organizationId,
      organizationType
    });
    await this.roleApiService.generateDefaultRolesPermissions({
      entityType: EntityType.PL_ETAT_CIVIL,
      organizationId,
      organizationType
    });

    await this.organizationRepository.updateOrganizationDefaultRoles({
      adminRoleId: adminRole.id,
      defaultRoleId: collabRole.id,
      organizationId
    });

    return { adminRoleId: adminRole.id };
  }
}
