import { PlaneteDossierRepository } from '@mynotary/backend/planete/core';
import { Injectable } from '@nestjs/common';
import { convertEnum, Exception, NotFoundError } from '@mynotary/crossplatform/shared/util';
import { planete_dossier, PrismaService } from '@mynotary/backend/shared/prisma-infra';
import {
  NewPlaneteDossier,
  PlaneteDossier,
  PlaneteDossierByStatus,
  PlaneteStatus,
  ProduitsPlanete
} from '@mynotary/crossplatform/api-adsn/api';

@Injectable()
export class PlaneteDossierRepositoryImpl extends PlaneteDossierRepository {
  constructor(private prisma: PrismaService) {
    super();
  }

  async getById(id: string) {
    let dossierDb: planete_dossier | null;
    try {
      dossierDb = await this.prisma.planete_dossier.findUnique({
        where: { id }
      });
    } catch (e) {
      throw new Exception(`Error fetching dossier ${id}`, { cause: e });
    }
    if (!dossierDb) {
      throw new NotFoundError({ id, resource: 'dossier' });
    }
    return this.fromDb(dossierDb);
  }

  async find({ conversationId }: Partial<PlaneteDossier>) {
    let dossiersDb: planete_dossier[];
    try {
      dossiersDb = await this.prisma.planete_dossier.findMany({
        where: {
          conversation_id: conversationId
        }
      });
    } catch (e) {
      throw new Exception(`Error fetching dossiers`, { cause: e });
    }
    return dossiersDb.map(this.fromDb);
  }

  async update(dossier: PlaneteDossier) {
    try {
      await this.prisma.planete_dossier.update({
        data: {
          conversation_id: dossier.conversationId ?? null,
          label: dossier.label,
          last_modified_time: new Date(),
          operation_id: dossier.operationId ? Number(dossier.operationId) : null,
          protocol: dossier.protocol,
          recipient_id: dossier.recipientId ?? null,
          status: dossier.status,
          sub_type: dossier.subType,
          type: dossier.type,
          user_id: dossier.userId ? Number(dossier.userId) : undefined
        },
        where: { id: dossier.id }
      });
    } catch (e) {
      throw new Exception(`Error updating dossier ${dossier.id}`, { cause: e });
    }
  }

  async create(dossier: NewPlaneteDossier) {
    try {
      const result = await this.prisma.planete_dossier.create({
        data: {
          conversation_id: dossier.conversationId ?? null,
          creation_time: new Date(),
          label: dossier.label,
          last_modified_time: new Date(),
          operation_id: dossier.operationId ? Number(dossier.operationId) : null,
          protocol: dossier.protocol,
          recipient_id: dossier.recipientId ?? null,
          status: dossier.status,
          sub_type: dossier.subType,
          type: dossier.type,
          user_id: dossier.userId ? Number(dossier.userId) : undefined
        }
      });
      return this.fromDb(result);
    } catch (e) {
      throw new Exception('Error creating dossier', { cause: e });
    }
  }

  async getDossiersByStatus(organizationId: string): Promise<PlaneteDossierByStatus[]> {
    try {
      const groups = await this.prisma.planete_dossier.groupBy({
        _count: true,
        by: 'status',
        where: {
          legal_component_operation: {
            legal_component: {
              organization_id: Number(organizationId)
            }
          }
        }
      });
      return groups.map(
        (group) =>
          ({
            count: group._count,
            status: convertEnum(PlaneteStatus, group.status)
          }) satisfies PlaneteDossierByStatus
      );
    } catch (e) {
      throw new Exception(`Error counting PlaneteDossiers by status`, { cause: e });
    }
  }

  private fromDb(dossierDb: planete_dossier): PlaneteDossier {
    return {
      conversationId: dossierDb.conversation_id ?? undefined,
      date: dossierDb.creation_time,
      id: dossierDb.id,
      label: dossierDb.label,
      lastModifiedTime: dossierDb.last_modified_time,
      operationId: dossierDb.operation_id ? dossierDb.operation_id.toString() : undefined,
      protocol: dossierDb.protocol,
      recipientId: dossierDb.recipient_id ?? undefined,
      status: dossierDb.status,
      subType: dossierDb.sub_type ?? undefined,
      type: convertEnum(ProduitsPlanete, dossierDb.type),
      userId: dossierDb.user_id ? dossierDb.user_id.toString() : undefined
    };
  }
}
