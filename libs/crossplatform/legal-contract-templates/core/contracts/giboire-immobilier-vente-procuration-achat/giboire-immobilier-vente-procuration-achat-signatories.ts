import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const acquereursSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__ACQUEREURS'
  });

  return [acquereursSignatories].flat();
}

export const GiboireImmobilierVenteProcurationAchatSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
