// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const I3FImmobilierBrsPreliminaireBonSouscription: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR: {
        branches: {
          ACQUEREUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__CONTRAT_PRELIMINAIRE: {
        branches: {
          CONTRAT_PRELIMINAIRE: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      defaultSignatories: {
        OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR: {
          branches: {
            ACQUEREUR: true
          }
        }
      },
      mandatoryDocuments: [],
      creationLockedAnswers: {}
    },
    registeredLetter: {}
  },
  id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_BON_SOUSCRIPTION',
  jeffersonPath: 'mynotary/i3F/immobilier/brsPreliminaire/bonSouscription.json',
  label: 'Bulletin de souscription',
  mainContract: false,
  originTemplate: null
};
