import {
  GetDefaultSignatoriesArgs,
  getMention,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory,
  getBailBailleurOrSignataireAgenceSignatories
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const bailleursOrRepresentantSignatories = getBailBailleurOrSignataireAgenceSignatories({ ctx });

  const locatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__LOCATAIRES'
  });
  const garantsSignatories = createDefaultSignatoriesFromRecords({
    config: {
      mention: getMention({
        ctx,
        mentions: {
          text: "Je m'engage en qualité de caution à payer à {{ BAILLEUR }}, ce que lui doit {{ LOCATAIRE }}, le LOCATAIRE dans la présente mention, en cas de défaillance de celui-ci, dans le paiement des loyers, complément de loyer si exigible, charges, et tous les frais accessoires y relatifs dont les éventuels frais de procédure, dans la limite des montants dûs par le locataire, d'un montant maximum de {{ PRIX }}. Je reconnais également ne pas pouvoir exiger du BAILLEUR qu'il poursuive d'abord le LOCATAIRE ou qu'il divise ses poursuites entre les autres cautions éventuelles",
          variables: {
            BAILLEUR: {
              items: [
                { type: 'QUESTION_ID', value: 'personne_morale_denomination' },
                { type: 'QUESTION_ID', value: 'personne_morale_forme_sociale', withPrefix: ' ' },
                { type: 'QUESTION_ID', value: 'prenoms' },
                { type: 'QUESTION_ID', value: 'nom', withPrefix: ' ' }
              ],
              path: ['OPERATION__IMMOBILIER__LOCATION__BAILLEURS', 'BRANCHES', 'BAILLEUR', 'RECORDS'],
              type: 'CONCAT'
            },
            LOCATAIRE: {
              items: [
                { type: 'QUESTION_ID', value: 'prenoms' },
                { type: 'QUESTION_ID', value: 'nom', withPrefix: ' ' }
              ],
              path: ['OPERATION__IMMOBILIER__LOCATION__LOCATAIRES', 'BRANCHES', 'LOCATAIRE', 'RECORDS'],
              type: 'CONCAT'
            },
            PRIX: {
              items: [{ type: 'QUESTION_ID', value: 'garantie_cautionnement_montant_total' }],
              path: ['OPERATION__IMMOBILIER__LOCATION__FICHES', 'BRANCHES', 'LOCATION', 'RECORDS'],
              type: 'ADD'
            }
          }
        }
      })
    },
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__GARANTS'
  });

  return [bailleursOrRepresentantSignatories, locatairesSignatories, garantsSignatories].flat();
}

export const ImmobilierLocationBailSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
