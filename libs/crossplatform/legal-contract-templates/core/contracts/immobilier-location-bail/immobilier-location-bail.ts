// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const ImmobilierLocationBail: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__LOCATION__FICHES: {
        branches: {
          LOCATION: {
            constraints: {
              min: 1,
              max: 1
            }
          },
          MANDAT: {
            constraints: {
              min: 1,
              max: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
        branches: {
          LOCATAIRE: {
            constraints: {
              min: 0
            },
            recordLinks: ['SITUATION_MARITALE', 'CAPACITE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BAILLEURS: {
        branches: {
          BAILLEUR: {
            constraints: {
              min: 0
            },
            recordLinks: ['SITUATION_MARITALE', 'CAPACITE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__GARANTS: {
        condition: {
          id: 'GARANTIE_CAUTIONNEMENT',
          legalRecordTemplate: 'RECORD__OPERATION__IMMOBILIER__LOCATION__LOCATION',
          questionId: 'garantie_cautionnement',
          value: 'oui'
        },
        branches: {
          GARANT: {
            constraints: {
              min: 1,
              contractSpecific: true
            },
            recordLinks: ['SITUATION_MARITALE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES: {
        branches: {
          BIEN_LOUE: {
            constraints: {
              min: 0
            },
            recordLinks: ['COMPOSITION__COPROPRIETE', 'COMPOSITION__ENSEMBLE_IMMOBILIER', 'COMPOSITION__LOT_ANNEXE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__SIGNATAIRE_AGENCE: {
        condition: {
          id: 'SIGNATAIRE_AGENCE',
          legalRecordTemplate: 'RECORD__OPERATION__IMMOBILIER__LOCATION__LOCATION',
          questionId: 'bail_representation_agence',
          value: 'oui'
        },
        branches: {
          SIGNATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__LOCATION__BAILLEURS', 'BRANCHES', 'BAILLEUR', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        },
        {
          recordPath: ['OPERATION__IMMOBILIER__LOCATION__LOCATAIRES', 'BRANCHES', 'LOCATAIRE', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        },
        {
          recordPath: ['OPERATION__IMMOBILIER__LOCATION__GARANTS', 'BRANCHES', 'GARANT', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__GARANTS',
            'BRANCHES',
            'GARANT',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__GARANTS',
            'BRANCHES',
            'GARANT',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__GARANTS',
            'BRANCHES',
            'GARANT',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        }
      ]
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {
        signatureType: {
          items: [
            {
              type: 'CONSTANT',
              constant: 'ELECTRONIC',
              condition: [
                [
                  {
                    comparator: 'EQUALS',
                    left: {
                      items: [
                        {
                          type: 'VARIABLE',
                          path: [
                            'records',
                            'OPERATION__IMMOBILIER__LOCATION__FICHES',
                            'LOCATION',
                            '0',
                            'signature_electronique',
                            'value'
                          ]
                        }
                      ]
                    },
                    right: {
                      items: [
                        {
                          type: 'CONSTANT',
                          constant: 'oui'
                        }
                      ]
                    }
                  }
                ]
              ]
            },
            {
              type: 'CONSTANT',
              constant: 'PAPER',
              condition: [
                [
                  {
                    comparator: 'EQUALS',
                    left: {
                      items: [
                        {
                          type: 'VARIABLE',
                          path: [
                            'records',
                            'OPERATION__IMMOBILIER__LOCATION__FICHES',
                            'LOCATION',
                            '0',
                            'signature_electronique',
                            'value'
                          ]
                        }
                      ]
                    },
                    right: {
                      items: [
                        {
                          type: 'CONSTANT',
                          constant: 'non'
                        }
                      ]
                    }
                  }
                ]
              ]
            }
          ],
          aggregate: {
            type: 'CONCAT'
          }
        }
      },
      defaultSignatories: {
        OPERATION__IMMOBILIER__LOCATION__BAILLEURS: {
          branches: {
            BAILLEUR: true
          }
        },
        OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
          branches: {
            LOCATAIRE: true
          }
        },
        OPERATION__IMMOBILIER__LOCATION__GARANTS: {
          config: {
            mention: {
              text: "Je m'engage en qualité de caution à payer à {{ BAILLEUR }}, ce que lui doit {{ LOCATAIRE }}, le LOCATAIRE dans la présente mention, en cas de défaillance de celui-ci, dans le paiement des loyers, complément de loyer si exigible, charges, et tous les frais accessoires y relatifs dont les éventuels frais de procédure, dans la limite des montants dûs par le locataire, d'un montant maximum de {{ PRIX }}. Je reconnais également ne pas pouvoir exiger du BAILLEUR qu'il poursuive d'abord le LOCATAIRE ou qu'il divise ses poursuites entre les autres cautions éventuelles",
              variables: {
                BAILLEUR: {
                  type: 'CONCAT',
                  path: ['OPERATION__IMMOBILIER__LOCATION__BAILLEURS', 'BRANCHES', 'BAILLEUR', 'RECORDS'],
                  items: [
                    {
                      type: 'QUESTION_ID',
                      value: 'personne_morale_denomination'
                    },
                    {
                      type: 'QUESTION_ID',
                      value: 'personne_morale_forme_sociale',
                      withPrefix: ' '
                    },
                    {
                      type: 'QUESTION_ID',
                      value: 'prenoms'
                    },
                    {
                      type: 'QUESTION_ID',
                      value: 'nom',
                      withPrefix: ' '
                    }
                  ]
                },
                LOCATAIRE: {
                  type: 'CONCAT',
                  path: ['OPERATION__IMMOBILIER__LOCATION__LOCATAIRES', 'BRANCHES', 'LOCATAIRE', 'RECORDS'],
                  items: [
                    {
                      type: 'QUESTION_ID',
                      value: 'prenoms'
                    },
                    {
                      type: 'QUESTION_ID',
                      value: 'nom',
                      withPrefix: ' '
                    }
                  ]
                },
                PRIX: {
                  items: [{ type: 'QUESTION_ID', value: 'garantie_cautionnement_montant_total' }],
                  path: ['OPERATION__IMMOBILIER__LOCATION__FICHES', 'BRANCHES', 'LOCATION', 'RECORDS'],
                  type: 'ADD'
                }
              }
            }
          },
          branches: {
            GARANT: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'IMMOBILIER_LOCATION_BAIL',
  jeffersonPath: 'mynotary/immobilier/location/bail.json',
  label: "Bail d'habitation",
  mainContract: true,
  originTemplate: 'IMMOBILIER_LOCATION_BAIL'
};
