import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const bailleursSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__BAILLEURS'
  });

  return [bailleursSignatories].flat();
}

export const ImmobilierLocationProcationLocationBailleurSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
