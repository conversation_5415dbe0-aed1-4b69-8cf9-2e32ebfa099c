// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const ImmobilierVenteAncienVerificationIdentiteAvancee: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE__VENDEURS: {
        branches: {
          VENDEUR: {
            constraints: {
              min: 0,
              contractSpecific: true
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__ACQUEREURS: {
        branches: {
          ACQUEREUR: {
            constraints: {
              min: 0,
              contractSpecific: true
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: []
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {
        signatureType: {
          items: [
            {
              type: 'CONSTANT',
              constant: 'SIGNATURE_AVANCEE'
            }
          ]
        }
      },
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__VENDEURS: {
          branches: {
            VENDEUR: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__ACQUEREURS: {
          branches: {
            ACQUEREUR: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'IMMOBILIER_VENTE_ANCIEN_VERIFICATION_IDENTITE_AVANCEE',
  jeffersonPath: 'mynotary/immobilier/venteAncien/verificationIdentiteAvancee.json',
  label: "Lettre pour vérification d'identité renforcée - Par signature Avancée",
  mainContract: false,
  originTemplate: null
};
