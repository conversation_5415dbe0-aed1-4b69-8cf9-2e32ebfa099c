import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const employesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__RECRUTEMENT__GENERAL__EMPLOYE'
  });
  const syndicatsSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__RECRUTEMENT__GENERAL__SYNDICAT'
  });

  return [employesSignatories, syndicatsSignatories].flat();
}

export const UnisSocialCddCategorieASignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
