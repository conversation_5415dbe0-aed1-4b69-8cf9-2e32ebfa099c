// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationImmobilierLocationNouveauLocataire: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Nouveau Locataire',
      labelPlural: 'Nouveaux Locataires',
      labelWithArticle: 'des locataires',
      branches: {
        NOUVEAU_LOCATAIRE: {
          label: 'Nouveau Locataire',
          labelWithArticle: 'des locataires'
        }
      }
    },
    branches: {
      NOUVEAU_LOCATAIRE: {
        type: 'NOUVEAU_LOCATAIRE',
        reverseType: 'NOUVEAU_LOCATAIRE',
        to: {
          type: 'RECORD',
          specificTypes: [
            ['PERSONNE', 'PHYSIQUE'],
            ['PERSONNE', 'MORALE']
          ]
        },
        constraints: {
          min: 1
        },
        links: [
          {
            specificTypes: ['SITUATION_MARITALE', 'MARIAG<PERSON>']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'DIVORCE']
          },
          {
            specificTypes: ['REPRESENTATION', 'PERSONNE_MORALE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'PACS']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'VEUVAGE']
          },
          {
            specificTypes: ['CAPACITE', 'SAUVEGARDE_JUSTICE']
          },
          {
            specificTypes: ['CAPACITE', 'CURATELLE']
          },
          {
            specificTypes: ['CAPACITE', 'EMANCIPATION']
          },
          {
            specificTypes: ['CAPACITE', 'HABILITATION_FAMILIALE']
          },
          {
            specificTypes: ['CAPACITE', 'TUTELLE']
          },
          {
            specificTypes: ['CAPACITE', 'MINORITE']
          },
          {
            specificTypes: ['CAPACITE', 'MANDAT_PROTECTION_FUTURE']
          },
          {
            specificTypes: ['PROCURATION', 'PROCURATION']
          },
          {
            specificTypes: ['CAPACITE', 'AUCUN']
          },
          {
            specificTypes: ['PROCURATION', 'AUCUN']
          },
          {
            specificTypes: ['CAPACITE', 'NON_DEFINI']
          }
        ],
        linkMatches: [
          ['SITUATION_MARITALE', '*'],
          ['REPRESENTATION', '*'],
          ['CAPACITE', '*'],
          ['PROCURATION', '*']
        ]
      }
    }
  },
  id: 'LINK__OPERATION__IMMOBILIER__LOCATION__NOUVEAU_LOCATAIRE',
  label: 'Nouveau locataire',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'NOUVEAU_LOCATAIRE'],
  type: 'LINK'
};
