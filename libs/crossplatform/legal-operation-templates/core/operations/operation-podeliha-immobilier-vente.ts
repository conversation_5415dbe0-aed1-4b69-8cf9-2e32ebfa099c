// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationPodelihaImmobilierVente: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__FICHE_VEFA',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS',
        order: 0,
        max: 1
      },
      acquereurs: {
        link: 'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
        order: 2,
        max: 1
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'VENTE', 'ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'VENTE', 'FICHE_VEFA'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'VENTE', 'FICHE_PSLA'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'VENTE', 'NOTAIRE_ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'FICHE_PSLA'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'FICHE_VEFA'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'PROMOTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'REPRESENTANT_PROMOTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__FICHE_VEFA',
      'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__FICHE_PSLA',
      'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__PROMOTEUR',
      'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__REPRESENTANT_PROMOTEUR',
      'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'PODELIHA', 'IMMOBILIER', 'PROGRAMME', 'VENTES']
      }
    ],
    contracts: [
      {
        id: 'PODELIHA_IMMOBILIER_VEFA_RESERVATION_CONTRAT_RESERVATION_PODELIHA',
        label: "Contrat de réservation - Groupe d'habitation"
      },
      {
        id: 'PODELIHA_IMMOBILIER_PSLA_PRELIMINAIRE_PSLA_PRELIMINAIRE',
        label: 'Contrat Préliminaire - PSLA'
      },
      {
        id: 'PODELIHA_IMMOBILIER_TERRAIN_VENTE_TERRAIN_AMENAGEUR',
        label: 'Compromis Terrain - Aménageur'
      },
      {
        id: 'PODELIHA_IMMOBILIER_TERRAIN_VENTE_TERRAIN_VIABILISE',
        label: 'Compromis Terrain - Viabilisé'
      },
      {
        id: 'PODELIHA_IMMOBILIER_VEFA_RESERVATION_CONTRAT_RESERVATION_INDIVIDUEL_PODELIHA',
        label: 'Contrat de réservation - Bien Individuel'
      },
      {
        id: 'PODELIHA_IMMOBILIER_VENTE_CONTRAT_RESERVATION_TRANCHE_DEUX',
        label: "Contrat de réservation - Groupe d'habitation Tranche 2"
      },
      {
        id: 'PODELIHA_IMMOBILIER_VENTE_CONTRAT_RESERVATION_INDIVIDUEL_TRANCHE_2',
        label: 'Contrat de réservation - Bien Individuel - Tranche 2'
      },
      {
        id: 'PODELIHA__IMMOBILIER__BRS__CESSION_BRS',
        label: 'Contrat de cession partielle de VEFA BRS - Ideal'
      },
      {
        id: 'PODELIHA__IMMOBILIER__BRS__BRS_BARATERIE',
        label: 'Contrat de réservation BRS - Modèle Baraterie'
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'erp_2',
      'dossier_communal',
      'dossier_communal_2',
      'georisques',
      'georisques_2',
      'plan_lot',
      'podeliha_assurance',
      'podeliha_assurance_pinel',
      'podeliha_bulletin',
      'podeliha_financement',
      'podeliha_impot',
      'podeliha_impot_pinel',
      'programme_plan',
      'declaration_honneur'
    ]
  },
  id: 'OPERATION__PODELIHA__IMMOBILIER__VENTE',
  label: 'Podeliha - Vente',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_NEUF',
  specificTypes: ['PODELIHA', 'IMMOBILIER', 'VENTE'],
  type: 'OPERATION'
};
