// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationImmobilierPslaPslaProgramme: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          id: 'programme_nom',
          label: 'Nom du programme',
          type: 'TEXT'
        },
        {
          id: 'programme_ville',
          label: 'Ville du programme',
          type: 'TEXT'
        },
        {
          id: 'programme_adresse',
          label: 'Adresse du programme',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_psla_vefa',
          label: "Les biens objets du PSLA font-ils l'objet d'une acquisition en VEFA par le Bailleur social ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'O<PERSON>'
            }
          ],
          conditions: [
            [
              {
                id: 'programme_psla_vefa',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'programme_psla_vefa_acquis',
          label: 'Le contrat de VEFA définitif a-t-il déjà été signé ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'programme_psla_vefa',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'programme_psla_vefa_acquis',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'programme_psla_vefa_acquis_date',
          label: "Date de signature de l'acte de VEFA définitif",
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'programme_psla_vefa',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'programme_psla_vefa_acquis',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'programme_psla_vefa_reservation_date',
          label: "Date de signature de l'acte de réservation en VEFA",
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'programme_psla_vefa',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'programme_psla_vefa_acquis',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'programme_psla_vefa_reservation_vendeur',
          label: 'Nom du vendeur en VEFA',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_travaux_statut',
          label: 'Les travaux de réalisation du programme ont-ils débutés ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_duree_livraison',
          label: 'Trimestre ou date maximale de livraison (en texte)',
          type: 'TEXT'
        },
        {
          id: 'programme_duree_livraison_date',
          label: 'Date maximale de livraison',
          type: 'DATE'
        },
        {
          id: 'programme_duree_previsionnelle_livraison',
          label: 'Début prévisionnel des travaux',
          type: 'TEXT'
        },
        {
          id: 'programme_duree_previsionnelle_mois_livraison',
          label: 'Délai global de construction',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_clarm',
          label: 'Le programme est porté par la CLARM',
          type: 'SELECT-BINARY'
        },
        {
          id: 'logement_nombre',
          label: 'Nombre de logements créés',
          type: 'DATE'
        },
        {
          id: 'logement_repartition',
          label: 'Répartition des logements',
          type: 'TEXT'
        }
      ],
      id: '8f8e4c19_ff8c_4472_8ef8_c336bbe4d403',
      label: 'Informations générales du programme',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'rcp_geometre_nom',
          label: 'Nom du géomètre ayant établi le RCP',
          type: 'TEXT'
        },
        {
          id: 'rcp_geometre_adresse',
          label: 'Adresse du géomètre ayant établi le RCP',
          type: 'ADDRESS'
        },
        {
          id: 'programme_terrain_superficie',
          label: 'Superficie du terrain objet du programme',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          id: 'programme_cadastre',
          label: 'Références cadastrales du terrain objet du programme',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_description_courte',
          label: 'Description courte du programme (nombre de bâtiments et de logement)',
          type: 'TEXT'
        },
        {
          id: 'programme_description',
          label: 'Description du projet du Réservant',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_brs_description',
          label: 'Description du programme',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_brs_edd_date',
          label: 'Date de réalisation du projet de règlement de copropriété',
          type: 'DATE'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'programme_projet_edd',
          label: 'Projet EDD-RCP',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_asl',
          label: 'Le programme est-il concerné par la création d’une ASL ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_aful',
          label: 'Le programme est-il concerné par la création d’une AFUL ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_zac',
          label: 'Le programme est-il concerné par une ZAC ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_asl_nom',
          label: "Nom de l'ASL",
          type: 'TEXT'
        },
        {
          id: 'programme_aful_nom',
          label: "Nom de l'AFUL",
          type: 'TEXT'
        },
        {
          id: 'programme_zac_nom',
          label: 'Nom de la ZAC',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_proprietaire',
          label: "Le Réservant est-il déjà propriétaire de l'assiette foncière de l'ensemble immobilier ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_origine',
          label: "Le contrat préliminaire doit-il mentionner l'origine de propriété ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_argiles',
          label: "L'ensemble immobilier est-il situé dans une zone de retrait de gonflement des argiles ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'zone_argile_faible',
              label: "Zone d'exposition faible"
            },
            {
              id: 'zone_argile_moyen',
              label: "Zone d'exposition moyenne"
            },
            {
              id: 'zone_argile_forte',
              label: "Zone d'exposition forte"
            }
          ],
          id: 'programme_argiles_zone',
          label: "Type de zone d'exposition",
          type: 'SELECT'
        },
        {
          id: 'programme_radon_classement',
          label: 'Classement de la commune concernant le RADON',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_brs_lotissement',
          label: 'Le programme est-il concerné par un lotissement ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_brs_lotissement_denomination',
          label: 'Dénomination du lotissement',
          type: 'TEXT'
        },
        {
          id: 'programme_brs_lotissement_date',
          label: "Date de l'arrêté autorisant le lotissement",
          type: 'DATE'
        },
        {
          id: 'programme_brs_lotissement_notaire',
          label: "Notaire ayant reçu l'acte de dépôt du lotissement",
          type: 'TEXT'
        },
        {
          id: 'programme_brs_asl_description',
          label: "Situation au regard de l'ASL",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_brs_volume',
          label: 'Programme concerné par une volumétrie',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_note_servitude',
          label: 'Ajouter une note de servitude',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_brs_avis_statut',
          label: "Les avis du Maire et du représentant de l'état ont-ils déjà été demandés?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_brs_op',
          label: 'Origine de propriété',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_brs_ehf',
          label: 'Un Etat hypothécaire a-t-il été demandé ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_brs_ehf_date',
          label: "Date de l'état hypothécaire",
          type: 'DATE'
        },
        {
          id: 'programme_brs_ehf_description',
          label: 'Description des inscriptions',
          type: 'TEXTAREA'
        },
        {
          conditions: [
            [
              {
                id: 'programme_brs_ehf',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'programme_ehf_doc',
          label: 'Etat hypothécaire',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'programme_brs_ehf',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'programme_mlv_doc',
          label: 'Accord de Mainlevée',
          type: 'UPLOAD'
        },
        {
          id: 'programme_brs_duree',
          label: 'Durée totale du BRS',
          suffix: 'ans',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_brs_servitude',
          label: 'Programme concerné par des servitudes',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_brs_servitude_description',
          label: 'Description des servitudes',
          type: 'TEXTAREA'
        },
        {
          conditions: [
            [
              {
                id: 'programme_brs_servitude',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'programme_servitude_doc',
          label: 'Rappel de servitude',
          type: 'UPLOAD'
        },
        {
          id: 'date_ca_validation_ofs',
          label: "Date du Conseil d'Administration validant le règlement de fonctionnement de l'OFS",
          type: 'DATE'
        },
        {
          id: 'programme_brs_couverture_travaux',
          label: 'Objet de la garantie contractuelle de gros travaux',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_brs_erp_date',
          label: "Date de l'état des risques et pollution",
          type: 'DATE'
        },
        {
          id: 'programme_brs_indice_bt',
          label: 'Montant du dernier indice BT01 publié',
          type: 'NUMBER'
        },
        {
          id: 'programme_plan_cadastre',
          label: 'Plan cadastral',
          type: 'UPLOAD'
        },
        {
          id: 'programme_dtg',
          label: 'Diagnostic Technique global',
          type: 'UPLOAD'
        },
        {
          id: 'programme_erp',
          label: 'Etat des risques',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'programme_erps',
          label: 'ERPS',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'georisques',
          label: 'Géorisques',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dossier_communal',
          label: 'Dossier communal',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'programme_brs_bail_projet',
          label: 'Projet de Bail Réel Solidaire',
          type: 'UPLOAD'
        },
        {
          id: 'contrat_reservation_acte_vente',
          label: 'Contrat de réservation / acte de vente du cédant',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_NOTE_CONDITIONS_ELIGIBILITE_PRENEUR'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'programme_brs_note_condition_eligibilite',
          label: "Note - Conditions d'éligibilité Preneur",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'programme_brs_garantie_complementaire',
          label: 'Garantie Complémentaire - Sécurisation HLM',
          type: 'UPLOAD'
        }
      ],
      id: 'a1d41604_2a0e_4026_a49f_90bcaf3793d9',
      label: 'Description du programme',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'permis_depose',
          label: 'Le permis est il déjà déposé',
          type: 'SELECT-BINARY'
        },
        {
          id: 'permis_description',
          label: 'Reprise et description du permis',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'permis_podeliha_obtenu',
              label: 'Le permis déjà été obtenu'
            },
            {
              id: 'permis_podeliha_demande',
              label: 'Une demande de permis a été déposée'
            },
            {
              id: 'permis_podeliha_aucun',
              label: "Aucun permis n'a encore été déposé"
            }
          ],
          id: 'programme_permis_podeliha',
          label: 'Concernant le permis de construire',
          type: 'SELECT'
        },
        {
          id: 'programme_permis_date_podeliha',
          label: 'Date de délivrance du permis de construire',
          type: 'DATE'
        },
        {
          id: 'programme_permis_numero_podeliha',
          label: 'Numéro du permis de construire',
          type: 'TEXT'
        },
        {
          id: 'programme_permis_depot_podeliha',
          label: 'Date de dépôt du permis de construire',
          type: 'DATE'
        },
        {
          id: 'programme_permis_depot_limite_podeliha',
          label: 'Date limite de dépôt du permis de construire',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_autorisations',
          label:
            "Les autorisations d'urbanisme nécessaires à la réalisation de l'ensemble immobilier ont-elles été obtenues ?",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'programme_autorisations_depot_date_extreme',
              label: 'Date extrême de dépôt du permis',
              type: 'DATE'
            },
            {
              id: 'programme_autorisations_numero',
              label: 'Numéro de permis',
              type: 'TEXT'
            },
            {
              id: 'programme_autorisations_recepisse',
              label: 'Numéro récepissé',
              type: 'TEXT'
            },
            {
              id: 'programme_permis_sp',
              label: 'Surface de plancher autorisée',
              type: 'NUMBER'
            },
            {
              id: 'programme_autorisations_depot_date',
              label: 'Date de dépôt du permis',
              type: 'DATE'
            },
            {
              id: 'programme_autorisations_date',
              label: 'Date de délivrance du permis',
              type: 'DATE'
            },
            {
              id: 'programme_autorisations_commune',
              label: 'Commune délivrant le permis',
              type: 'TEXT'
            },
            {
              id: 'programme_autorisations_objet',
              label: 'Objet / projet du permis',
              type: 'TEXT'
            },
            {
              id: 'programme_autorisations_huissier_nom',
              label: "Nom de l'huissier établissant les Procès Verbaux",
              type: 'TEXT'
            },
            {
              id: 'programme_autorisations_huissier_date',
              label: 'Date des Procès Verbaux',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'programme_autorisations_recours',
              label: "Le permis a fait l'objet d'un recours ou d'une contestation",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'programme_autorisations_modificatif',
              label: "Le permis initial a fait l'objet de modificatifs",
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'programme_autorisations_modificatif_liste_programme_autorisations_modificatif_numero',
                  label: 'Numéro de permis modificatif',
                  type: 'TEXT'
                },
                {
                  id: 'programme_autorisations_modificatif_liste_programme_autorisations_modificatif_depot_date',
                  label: 'Date de dépôt du permis modificatif',
                  type: 'DATE'
                },
                {
                  id: 'programme_autorisations_modificatif_liste_programme_autorisations_modificatif_date',
                  label: 'Date de délivrance du permis modificatif',
                  type: 'DATE'
                },
                {
                  id: 'programme_autorisations_modificatif_liste_programme_autorisations_modificatif_commune',
                  label: 'Commune délivrant le permis modificatif',
                  type: 'TEXT'
                },
                {
                  id: 'programme_autorisations_modificatif_liste_programme_autorisations_modificatif_objet',
                  label: 'Objet du modificatif',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'programme_autorisations_modificatif_liste_programme_autorisations_modificatif_recours',
                  label: "Le permis modificatif a fait l'objet d'un recours ou d'une contestation",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'programme_autorisations_modificatif_liste',
              label: 'Permis Modificatifs',
              repetition: {
                max: 1000,
                min: 0
              },
              type: 'REPEAT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'programme_autorisations_purge',
              label: 'Ces autorisations sont-elles purgées de tout recours ?',
              type: 'SELECT-BINARY'
            }
          ],
          id: '77a161bd_aa86_488d_8483_298f92445567',
          label: 'Permis',
          type: 'CATEGORY'
        },
        {
          id: 'programme_autorisation_ouverture_chantier_date',
          label: "Date d'ouverture du chantier",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_assurance',
          label: 'Les assurances dommage-ouvrage et décennale sont-elles déjà souscrites ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_travaux_acheves',
          label: 'Les travaux sont-ils déjà achevés ?',
          type: 'SELECT-BINARY'
        }
      ],
      id: '31842b30_06d2_4243_920c_d77ad7e78fac',
      label: 'Autorisations du Programme',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'organisme_rcp',
          label: 'Organisme assurant la RCP',
          type: 'TEXT'
        },
        {
          id: 'organisme_rcp_numero',
          label: 'Numéro de la RCP',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'organisme_do_statut',
          label: "L'assurance dommage-ouvrage est-elle déjà obtenue ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'organisme_do_nom',
          label: 'Organisme assurant la dommage-ouvrage',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_notification',
          label: 'Les notifications du programme (SRU, projet de VEFA) pourront-elles se faire par voie électronique ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_tma',
          label: 'Le programme inclut-il la possibilité de recourir à la procédure Travaux Modificatifs Acquéreurs ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_condition_suspensive',
          label: 'Ajouter une condition suspensive au profit du Réservant',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_condition_suspensive_clause',
          label: 'Clause de la condition suspensive',
          type: 'TEXTAREA'
        }
      ],
      id: '70db10ec_61d8_4034_9b45_94e3155394e4',
      label: 'Conditions du programme',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'redevance_brs_montant',
          label: 'Montant de la redevance BRS (€/m2)',
          type: 'PRICE'
        },
        {
          id: 'penalite_m2',
          label: "Montant de la pénalité en cas de non respect de l'affectation (€/m2)",
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_depot_garantie_statut',
          label: 'Le dépôt de garantie doit-il etre versé au jour de la signature ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_depot_garantie_cheque',
          label: 'Le dépôt de garantie peut être versé par chèque ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_depot_garantie_virement',
          label: 'Le dépôt de garantie peut être versé par virement ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'programme_depot_garantie_statut',
                type: 'EQUALS',
                value: 'non'
              }
            ]
          ],
          id: 'programme_depot_garantie_delai',
          label: 'Sous quel délai le dépôt de garantie est-il versé ?',
          suffix: 'jour',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              label: '10 % du prix de vente',
              id: 'dix_pourcent'
            },
            {
              label: '5 % du prix de vente',
              id: 'cinq_pourcent'
            },
            {
              label: 'un autre montant fixe',
              id: 'autre_montant'
            }
          ],
          id: 'programme_depot_garantie_montant',
          label: 'Le montant du dépôt de garantie est',
          type: 'SELECT'
        },
        {
          choices: [
            {
              label: '10 % du prix de vente',
              id: 'dix_pourcent'
            },
            {
              label: '5 % du prix de vente',
              id: 'cinq_pourcent'
            },
            {
              label: '2 % du prix de vente',
              id: 'deux_pourcent'
            },
            {
              label: '1 % du prix de vente',
              id: 'un_pourcent'
            }
          ],
          id: 'programme_depot_garantie_montant_axeliha',
          label: 'Le montant du dépôt de garantie est',
          type: 'SELECT'
        },
        {
          id: 'programme_depot_garantie_montant_autre',
          label: 'Montant du dépôt de garantie',
          type: 'PRICE'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'garantie_revente',
          label: 'Garanties revente',
          type: 'UPLOAD'
        }
      ],
      id: '36bcf268_a372_42ce_bf55_224cccd65fbf',
      label: 'Informations financières du Programme',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'notice',
          label: 'Notice descriptive',
          type: 'UPLOAD'
        },
        {
          id: 'plan_masse',
          label: 'Plan de masse',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_SECURISATION_CLESENCE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'document_securisation_clesence',
          label: 'Annexe au contrat préliminaire',
          type: 'UPLOAD'
        },
        {
          id: 'programe_erp',
          label: 'Etat des risques et pollution',
          type: 'UPLOAD'
        },
        {
          id: 'programe_plan_situation_ensemble_immobilier',
          label: "Plan de situation de l'ensemble immobilier",
          type: 'UPLOAD'
        },
        {
          id: 'programme_reglement_ofs',
          label: "Règlement de fonctionnement de l'OFS",
          type: 'UPLOAD'
        },
        {
          id: 'programme_agrement_ofs',
          label: "Agrément de l'OFS",
          type: 'UPLOAD'
        },
        {
          id: 'programme_agrement_acquereur',
          label: "Formulaire - demande agrément de l'acquéreur",
          type: 'UPLOAD'
        }
      ],
      id: '4e5086aa_d425_4dbf_bca1_099b03e4347f',
      label: 'Ensemble Immobilier',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'programme_promoteur_designation',
          label: 'Désignation du Promoteur',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'societe_sci',
              label: 'Société Civile Immobilière'
            },
            {
              id: 'societe_sarl',
              label: 'Société à Responsabilité Limitée'
            },
            {
              id: 'societe_sa',
              label: 'Société par Actions'
            },
            {
              id: 'societe_sas',
              label: 'Société par Actions Simplifiée'
            },
            {
              id: 'societe_sccv',
              label: 'Société Civile de Construction Vente'
            },
            {
              id: 'commune',
              label: 'Commune - Mairie'
            },
            {
              id: 'association',
              label: 'Association'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'programme_promoteur_forme_sociale',
          label: 'Forme Sociale',
          type: 'SELECT'
        },
        {
          conditions: [[]],
          id: 'programme_promoteur_forme_sociale_autre',
          label: 'Autre forme',
          type: 'TEXT'
        },
        {
          id: 'programme_promoteur_capital',
          label: 'Capital Social',
          type: 'PRICE'
        },
        {
          id: 'programme_promoteur_adresse',
          label: 'Adresse du Promoteur',
          type: 'ADDRESS'
        },
        {
          id: 'programme_promoteur_siren_numero',
          label: 'Siren',
          type: 'TEXT'
        },
        {
          id: 'programme_promoteur_siren_ville',
          label: "Ville d'immatriculation",
          type: 'TEXT'
        },
        {
          id: 'programme_promoteur_ensemble_immobilier',
          label: "Dénomination de l'ensemble Immobilier à construire",
          type: 'TEXTAREA'
        },
        {
          id: 'programme_promoteur_notaire_nom',
          label: 'Nom du notaire du promoteur',
          type: 'TEXT'
        },
        {
          id: 'programme_promoteur_notaire_prenom',
          label: 'Prénom du notaire du promoteur',
          type: 'TEXT'
        },
        {
          id: 'programme_promoteur_notaire_ville',
          label: 'Ville du notaire du promoteur',
          type: 'TEXT'
        }
      ],
      id: 'b272f057_5bff_4963_b3a2_ef5ead965543',
      label: 'Promoteur',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'date_depot_agrement',
          label: "Date de dépôt de la demande d'agrément auprès de la DDT",
          type: 'DATE'
        },
        {
          id: 'date_obtention_agrement',
          label: "Date provisoire d'obtention de la demande d'agrément",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'programme_1',
              label: '1 année'
            },
            {
              id: 'programme_2',
              label: '2 années'
            },
            {
              id: 'programme_3',
              label: '3 années'
            },
            {
              id: 'programme_4',
              label: '4 années'
            },
            {
              id: 'programme_5',
              label: '5 années '
            }
          ],
          id: 'programme_psla_duree_contrat_annee',
          label: 'Durée totale du contrat de location accession (en année)',
          type: 'SELECT'
        },
        {
          id: 'programme_psla_indice_valeur',
          label: "Valeur actuelle de l'indice des loyers en cours",
          type: 'NUMBER'
        },
        {
          id: 'programme_psla_indice_trimestre',
          label: "Trimestre de l'indice des loyers en cours",
          type: 'TEXT'
        },
        {
          id: 'programme_psla_indice_annee',
          label: "Année de l'indice des loyers en cours",
          type: 'YEAR'
        },
        {
          id: 'programme_psla_charge',
          label: 'Montant des charges par an par m2',
          type: 'PRICE'
        },
        {
          id: 'programme_psla_duree_location',
          label: 'Durée totale du contrat de location-accession',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_psla_convention_etat',
          label: "Une convention avec l'Etat a-t-elle été signée ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_psla_convention_etat_date',
          label: 'Date de signature de la convention',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_psla_agrement',
          label: "L'agrément à la réalisation des logements en PSLA a-t-il déjà été obtenu ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_psla_compagnie_assurance_nom',
          label: "Nom de la compagnie d'assurance du Bailleur",
          type: 'TEXT'
        },
        {
          id: 'programme_psla_arrete_erp_date',
          label: "Date de l'arrêté préfectoral concernant les risques naturels",
          type: 'DATE'
        },
        {
          id: 'programme_psla_arrete_erp_numero',
          label: "Numéro de l'arrêté préfectoral concernant les risques naturels",
          type: 'TEXT'
        },
        {
          id: 'programme_psla_organisme',
          label: "Nom de l'organisme délivrant l'agrément",
          type: 'TEXT'
        },
        {
          id: 'programme_psla_date_agrement',
          label: "Date de l'agrément",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_psla_resiliation',
          label: 'Une indemnité de résiliation est-elle due en cas de faute du Réservataire ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_psla_minoration',
          label: 'Le prix de vente du Bien objet du PSLA subit-il une minoration chaque année ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'programme_psla_minoration',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'programme_psla_minoration_montant',
          label: 'Montant de la minoration',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'psla_pret_reservant_obtenu',
              label: 'A déjà été obtenu'
            },
            {
              id: 'psla_pret_reservant_engagement',
              label: "Fait l'objet d'un engagement"
            },
            {
              id: 'psla_pret_reservant_demande',
              label: "N'a pas encore fait l'objet d'une demande"
            }
          ],
          id: 'programme_psla_pret_reservant_statut',
          label: "Concernant le financement de l'opération par le Réservant, le PSLA",
          type: 'SELECT'
        },
        {
          id: 'programme_psla_pret_montant_total',
          label: 'Montant du prêt PSLA',
          type: 'PRICE'
        },
        {
          id: 'programme_psla_pret_obtention_date',
          label: "Date d'obtention du PSLA",
          type: 'DATE'
        },
        {
          id: 'programme_psla_pret_engagement_date',
          label: "Date d'obtention de l'engagement de financement",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'cs_podeliha_proprietaire',
              label: 'Que le réservant devienne propriétaire du bien'
            },
            {
              id: 'cs_podeliha_permis',
              label: 'Obtention du permis de construire, purgé'
            },
            {
              id: 'cs_podeliha_psla',
              label: 'Obtention du PSLA par le réservant'
            },
            {
              id: 'cs_podeliha_convention',
              label: "Obtention d'une convention d'agrément avec l'état"
            },
            {
              id: 'cs_podeliha_autre',
              label: 'Autre'
            }
          ],
          id: 'programme_cs_podeliha',
          label: 'Conditions suspensives du programme',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'programme_cs_podeliha_compromis',
          label: 'Le compromis de vente portant sur le terrain a-t-il déjà été signé ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'programme_cs_podeliha_compromis_proprietaire',
          label: 'Nom du propriétaire vendeur du terrain',
          type: 'TEXT'
        },
        {
          id: 'programme_cs_podeliha_compromis_date',
          label: 'Date de signature du compromis de vente',
          type: 'DATE'
        },
        {
          id: 'programme_cs_autre_podeliha',
          label: 'Autres conditions suspensives',
          type: 'TEXT'
        },
        {
          id: 'programme_clarm_convention_duree',
          label: "Durée de la convention avec l'état",
          suffix: 'ans',
          type: 'NUMBER'
        },
        {
          id: 'programme_clarm_convention_debut',
          label: 'Date de début de la convention',
          type: 'DATE'
        },
        {
          id: 'programme_clarm_convention_fin',
          label: 'Date de fin de la convention',
          type: 'DATE'
        },
        {
          id: 'programme_clarm_convention_engagement',
          label: 'Engagements pris par',
          type: 'TEXT'
        },
        {
          id: 'programme_clarm_convention_engagement_duree',
          label: "Durée de l'engagement",
          suffix: 'ans',
          type: 'NUMBER'
        }
      ],
      id: '1dd040b6_4fd8_454d_ac61_ab56d72ff016',
      label: 'Conditions propres au PSLA',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'parties_privatives_diagnostiqueur_nom',
          label: 'Nom du diagnostiqueur',
          type: 'TEXT'
        },
        {
          id: 'parties_privatives_diagnostiqueur_adresse',
          label: 'Adresse du diagnostiqueur',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'commune_pprn',
          label: 'Commune concernée par un plan de prévention des risques naturels',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'commune_pprn_concernee',
          label: 'Parcelle concernée par ces risques naturels',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'commune_pprt',
          label: 'Commune concernée par un plan de prévention des risques techonlogiques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'commune_pprt_concernee',
          label: 'Parcelle concernée par ces risques technologiques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'radon_zone',
          label: 'Zone RADON 3',
          type: 'SELECT-BINARY'
        },
        {
          id: 'zone_sismicite',
          label: 'Zone de sismicité',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sis_zone',
          label: "Zone d'information sur les sols",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'aucun',
              label: 'Aucun plan'
            },
            {
              id: 'prescrit',
              label: 'Plan prescrit'
            },
            {
              id: 'applique',
              label: 'Plan appliqué par anticipation'
            },
            {
              id: 'approuve',
              label: 'Plan approuvé'
            }
          ],
          id: 'pprn',
          label: 'Risques naturels',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'aucun',
              label: 'Aucun plan'
            },
            {
              id: 'prescrit',
              label: 'Plan prescrit'
            },
            {
              id: 'applique',
              label: 'Plan appliqué par anticipation'
            },
            {
              id: 'approuve',
              label: 'Plan approuvé'
            }
          ],
          id: 'pprm',
          label: 'Risques miniers',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'aucun',
              label: 'Aucun plan'
            },
            {
              id: 'prescrit',
              label: 'Plan prescrit'
            },
            {
              id: 'applique',
              label: 'Plan appliqué par anticipation'
            },
            {
              id: 'approuve',
              label: 'Plan approuvé'
            }
          ],
          id: 'pprt',
          label: 'Risques technologiques',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'geotechnique_statut',
          label: 'Une étude des sols a été réalisée',
          type: 'SELECT-BINARY'
        },
        {
          id: 'geotechnique_nom',
          label: "Nom de la société réalisant l'étude",
          type: 'TEXT'
        },
        {
          id: 'geotechnique_date',
          label: "Date de réalisation de l'étude",
          type: 'TEXT'
        },
        {
          id: 'geotechnique_resultat',
          label: "Résultat de l'étude",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sinistre_indemnite',
          label: 'Indemnité',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sinistre_declaration',
          label: 'Déclaration de sinistre effectué',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sinistre_indemnite_specifique',
          label: 'Indemnistation spécifique - catastrophe naturelle et technologique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '9e172807_4f53_495a_972c_0909a66006e6',
      label: 'Diagnostics',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'programme_reprise_vefa_modalites_execution_travaux',
          label: "Modalités d'exécution des travaux",
          type: 'TEXTAREA'
        },
        {
          id: 'programme_reprise_vefa_definition_achevement',
          label: "Définition de l'achèvement",
          type: 'TEXTAREA'
        },
        {
          id: 'programme_reprise_vefa_date_previsionnelle_livraison',
          label: 'Date prévisionnelle de livraison',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_reprise_vefa_operations_prealables_livraison',
          label: 'Opérations préalables à la livraison',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_reprise_vefa_constatation_livraison',
          label: "Constatation de l'achèvement - livraison",
          type: 'TEXTAREA'
        },
        {
          id: 'programme_reprise_vefa_verification_conformite_obligations',
          label: 'Véfification de la conformité aux obligations contractuelles',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_reprise_vefa_conformite_administrative',
          label: 'Conformité administrative des biens',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_reprise_vefa_garanties_vices',
          label: 'Garanties des vices et non conformités apparents',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_reprise_vefa_garantie_bancaire',
          label: "Garantie bancaire d'achèvement",
          type: 'TEXTAREA'
        },
        {
          id: 'programme_reprise_vefa_assurances_obligatoires',
          label: 'Assurances obligatoires',
          type: 'TEXTAREA'
        }
      ],
      id: '521e2681_1fb0_4378_916f_616c45fb4b31',
      label: 'Reprise du contrat de VEFA - Extrait littéral',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__IMMOBILIER__PSLA__PSLA_PROGRAMME',
  label: 'Programme',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__IMMOBILIER__PSLA__PSLA_PROGRAMME',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'PSLA', 'PSLA_PROGRAMME'],
  type: 'RECORD'
};
