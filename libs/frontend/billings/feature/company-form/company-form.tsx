import { FormCard } from '@mynotary/frontend/shared/ui';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectDraftSubscription,
  updateSubscriptionBillings,
  updateSubscriptionTax
} from '@mynotary/frontend/billings/store';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { useEffect, useMemo, useState } from 'react';
import { Form, useFormMode } from '@mynotary/frontend/legals/api';
import { PlanType, getPlan } from '@mynotary/crossplatform/billings/core';
import { map } from 'lodash';
import { mergeAndCopyAnswer } from '@mynotary/frontend/legals/api';
import { FormQuestion, NumberFormQuestion, SelectFormQuestion } from '@mynotary/crossplatform/shared/forms-util';

const getForm = (planType: PlanType): Array<NumberFormQuestion | FormQuestion | SelectFormQuestion> => {
  const taxes = getPlan(planType).taxes;
  return [
    {
      format: '### ### ###',
      id: 'siren',
      label: 'SIREN',
      required: true,
      type: 'NUMBER'
    },
    {
      id: 'address',
      label: 'Adresse de facturation',
      required: true,
      type: 'ADDRESS'
    },
    {
      id: 'denomination',
      label: 'Dénomination sociale',
      required: true,
      type: 'TEXT'
    },
    {
      choices: map(taxes, ({ id, name }) => ({
        id: `${id}`,
        label: name
      })),
      id: 'tva',
      label: 'TVA',
      placeholder: 'Choisir une TVA',
      required: true,
      type: 'SELECT'
    }
  ];
};

export const CompanyForm = () => {
  const dispatch = useDispatch();
  const draftSubscription = useSelector(selectDraftSubscription);
  const [answer, setAnswer] = useState<AnswerDict>();
  const formMode = useFormMode();
  const form = useMemo(
    () => (draftSubscription?.planType ? getForm(draftSubscription?.planType) : []),
    [draftSubscription?.planType]
  );

  useEffect(() => {
    setAnswer({
      address: { value: draftSubscription?.customer?.address },
      denomination: { value: draftSubscription?.customer?.organizationName },
      siren: { value: draftSubscription?.customer?.siren },
      tva: { value: draftSubscription?.taxType }
    });
  }, [draftSubscription?.customer, draftSubscription?.taxType]);

  const handleAnswerChange = (update: AnswerDict) => {
    const finalAnswer = mergeAndCopyAnswer(answer, update);
    dispatch(updateSubscriptionTax(finalAnswer['tva']?.value));
    dispatch(
      updateSubscriptionBillings({
        address: update['address']?.value ?? draftSubscription?.customer?.address,
        organizationName: update['denomination']?.value ?? draftSubscription?.customer?.organizationName,
        siren: update['siren']?.value?.toString() ?? draftSubscription?.customer?.siren
      })
    );
  };

  return (
    <FormCard formTitle='Adresse de facturation'>
      <Form answer={answer} formMode={formMode} forms={form} onChange={handleAnswerChange} />
    </FormCard>
  );
};
