import { Counter } from '@mynotary/frontend/billings/ui';
import {
  selectCurrentSubscription,
  selectDraftSubscription,
  updateSubscriptionLicenceCount
} from '@mynotary/frontend/billings/store';
import { useDispatch, useSelector } from 'react-redux';
import { getDefaultLicenceCountPlan } from '@mynotary/frontend/billings/core';
import { setSnackbarMessage } from '@mynotary/frontend/snackbars/api';
import { pluralize } from '@mynotary/frontend/shared/util';

interface SubscriptionCounterProps {
  maxValue?: number;
  onUpdatingLicenceCount?: () => void;
}

const SubscriptionCounter = ({ maxValue = Infinity, onUpdatingLicenceCount }: SubscriptionCounterProps) => {
  const dispatch = useDispatch();
  const currentSubscription = useSelector(selectCurrentSubscription);
  const draftSubscription = useSelector(selectDraftSubscription);
  const defaultLicenceCountPlan = getDefaultLicenceCountPlan(draftSubscription?.planType);
  const minLicence = Math.max(defaultLicenceCountPlan, currentSubscription?.licenceUsedCount ?? 0);

  const updateLicenceCount = (licenceCount: number) => {
    if (licenceCount === minLicence) {
      if (defaultLicenceCountPlan === licenceCount) {
        dispatch(
          setSnackbarMessage({
            options: {
              color: 'info',
              title: `Nos offres incluent un minimum de ${defaultLicenceCountPlan} ${pluralize(
                'compte',
                defaultLicenceCountPlan
              )} ${pluralize('utilisateur', defaultLicenceCountPlan)} pour une organisation.`
            },
            templateId: 'PERMANENT'
          })
        );
      } else if (currentSubscription?.licenceUsedCount === licenceCount) {
        dispatch(
          setSnackbarMessage({
            options: {
              color: 'info',
              title: `Vous ne pouvez pas retirer des licences utilisées. Vous avez ${currentSubscription?.licenceUsedCount} ${pluralize(
                'licence',
                currentSubscription?.licenceUsedCount
              )} ${pluralize('utilisée', currentSubscription?.licenceUsedCount)} sur votre organisation.`
            },
            templateId: 'PERMANENT'
          })
        );
      }
    }

    dispatch(updateSubscriptionLicenceCount(licenceCount));
    onUpdatingLicenceCount?.();
  };

  const getStep = (value: number) => {
    if (value >= 50) {
      return 10;
    }
    return 1;
  };

  return (
    <Counter
      getStep={getStep}
      initialValue={draftSubscription?.licenceCount ?? currentSubscription?.licenceCount ?? defaultLicenceCountPlan}
      maxValue={maxValue}
      minValue={minLicence}
      onChange={updateLicenceCount}
    />
  );
};

export { SubscriptionCounter };
