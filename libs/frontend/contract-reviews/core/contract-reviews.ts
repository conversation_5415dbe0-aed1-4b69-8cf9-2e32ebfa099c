import { Task } from '@mynotary/frontend/legals/api';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';
import { Permission } from '@mynotary/crossplatform/roles/api';
import { User } from '@mynotary/frontend/shared/util';

export interface ContractReview {
  currentUser: User;
  features: FeatureType[];
  id: string;
  reviewContractTasks: Task[];
  status: ContractStatus;
  userPermissions: Permission[];
}

export type ContractReviewRequestNew = {
  contractId: string;
  description: string;
  emailAssignees: string[];
  emailContent: string;
  emailSubject: string;
  title: string;
  userId: string;
};

export type ContractReviewNew = {
  contractId: string;
  userId: string;
};

export type ContractReviewRequestCreated = {
  task: Task;
};

export type ContractReviewRequestDeleted = {
  deletedTaskId: string;
};
