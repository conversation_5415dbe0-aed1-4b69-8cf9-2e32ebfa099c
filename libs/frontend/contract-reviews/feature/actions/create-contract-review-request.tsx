import React, { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { selectContract, selectContractPermission } from '@mynotary/frontend/legals/api';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { openPopin } from '@mynotary/frontend/popins/api';
import { PopinType } from '@mynotary/frontend/popins/api';

interface TriggerElementProps {
  disabled?: boolean;
  onClick: () => void;
}

type CreateContractReviewRequestProps = {
  contractId: number;
  onFinish?: () => void;
  operationId: number;
  triggerElement: React.FC<TriggerElementProps>;
};

export const CreateContractReviewRequest = ({
  contractId,
  onFinish,
  operationId,
  triggerElement: TriggerElement
}: CreateContractReviewRequestProps): ReactElement | null => {
  const dispatch = useAsyncDispatch();
  const contract = useSelector(selectContract(contractId));
  const canCreateReviewRequest = useSelector(
    selectContractPermission(PermissionType.CREATE_CONTRACT_TASK_ASK_REVIEW, contractId)
  );

  const canShowCreateReviewRequest =
    canCreateReviewRequest && [ContractStatus.REDACTION, ContractStatus.VALIDATION_PENDING].includes(contract.status);

  if (!canShowCreateReviewRequest) {
    return null;
  }

  const handleCreateReviewRequest = () => {
    dispatch(openPopin({ contractId, operationId, type: PopinType.CONTRACT_REVIEW_REQUEST }));
    onFinish?.();
  };

  return <TriggerElement onClick={handleCreateReviewRequest} />;
};
