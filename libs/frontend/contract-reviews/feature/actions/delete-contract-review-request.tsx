import React, { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectContract, selectContractPermission } from '@mynotary/frontend/legals/api';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { ContractReviewsClient } from '@mynotary/frontend/contract-reviews/core';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';

interface TriggerElementProps {
  disabled?: boolean;
  onClick: () => void;
}

type DeleteContractReviewRequestProps = {
  contractId: number;
  onFinish?: () => void;
  triggerElement: React.FC<TriggerElementProps>;
};

export const DeleteContractReviewRequest = ({
  contractId,
  onFinish,
  triggerElement: TriggerElement
}: DeleteContractReviewRequestProps): ReactElement | null => {
  const dispatch = useAsyncDispatch();
  const [status, setStatus] = useState<'idle' | 'deleting' | 'success' | 'error'>('idle');
  const canCreateReviewRequest = useSelector(
    selectContractPermission(PermissionType.CREATE_CONTRACT_TASK_ASK_REVIEW, contractId)
  );
  const contract = useSelector(selectContract(contractId));
  const contractReviewsClient = useService(ContractReviewsClient);

  const canShowDeleteReviewRequest = canCreateReviewRequest && contract.status === ContractStatus.REVIEW_PENDING;

  if (!canShowDeleteReviewRequest) {
    return null;
  }

  const handleDeleteReviewRequest = async () => {
    try {
      setStatus('deleting');
      await contractReviewsClient.deleteReviewRequest(contractId.toString());
      setStatus('success');
      onFinish?.();
    } catch (error) {
      dispatch(setErrorMessage('Une erreur est survenue lors de la suppression de la demande de relecture'));
      console.error(error);
      setStatus('error');
    }
  };

  return <TriggerElement disabled={status === 'deleting'} onClick={handleDeleteReviewRequest} />;
};
