import { createAxiosInstance } from '@mynotary/frontend/shared/axios-util';
import {
  ContractReviewNew,
  ContractReviewRequestCreated,
  ContractReviewRequestDeleted,
  ContractReviewRequestNew,
  ContractReviewsClient
} from '@mynotary/frontend/contract-reviews/core';
import { environment } from '@mynotary/frontend/shared/environments-util';
import { ContractReviewDto, TaskDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { TaskType } from '@mynotary/crossplatform/legals/api';

export class ContractReviewsClientImpl implements ContractReviewsClient {
  apiClient = createAxiosInstance({ baseURL: environment.apiMyNotaryUrl });

  async createReviewRequest(args: ContractReviewRequestNew): Promise<ContractReviewRequestCreated> {
    const task = await this.apiClient.post<TaskDto>('/contract-review-requests', args);

    const commonField = {
      assignees: task.data.assignees.map((assignee) => ({
        taskId: parseInt(task.data.id),
        user: {
          email: assignee.email,
          firstname: assignee.firstname,
          id: assignee.id ? parseInt(assignee.id) : undefined,
          lastname: assignee.lastname
        }
      })),
      creationTime: new Date(task.data.creationTime).getTime(),
      creatorUser: {
        email: task.data.creatorEmail,
        firstname: task.data.creatorFirstname,
        lastname: task.data.creatorLastname,
        phone: task.data.creatorPhone,
        profilePictureFileId: task.data.creatorProfilePictureFileId
      },
      creatorUserId: parseInt(task.data.creatorUserId),
      description: task.data.description,
      emails: task.data.assignees.map((assignee) => assignee.email),
      id: parseInt(task.data.id),
      legalComponentId: parseInt(task.data.legalOperationId),
      organization: {
        address: task.data.organizationAddress,
        id: parseInt(task.data.organizationId),
        name: task.data.organizationName
      },
      seen: task.data.seen,
      title: task.data.title
    };

    assertNotNull(task.data.contractId, 'contractId');
    return {
      task: {
        ...commonField,
        reference: {
          contractId: parseInt(task.data.contractId)
        },
        type: TaskType.REVIEW_CONTRACT
      }
    };
  }

  async deleteReviewRequest(contractId: string): Promise<ContractReviewRequestDeleted> {
    const response = await this.apiClient.delete(`/contract-review-requests/${contractId}`);
    return response.data;
  }

  async validateReview(dto: ContractReviewNew): Promise<void> {
    await this.apiClient.post<void>('/contract-reviews', {
      contractId: dto.contractId,
      userId: dto.userId
    } satisfies ContractReviewDto);
  }

  async cancelReview(contractId: string): Promise<void> {
    await this.apiClient.delete(`/contract-reviews/${contractId}`);
  }
}
