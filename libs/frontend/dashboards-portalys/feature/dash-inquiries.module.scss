@use 'style/variables/colors' as *;

.dashInquiries {
  display: flex;
  flex-flow: row;
  height: 100vh;
}

.statusSelectionBar {
  display: flex;
  flex:1;
  flex-flow: column;
  align-items: center;

  padding: 24px;
  border-right: 1px solid $gray300;
}

.statuses {
  width:100%;
  padding: 16px 0;
  border-bottom: 1px solid $gray300;
}

.status {
  cursor: pointer;

  display: flex;
  flex-flow: row;
  justify-content: space-between;

  padding: 16px;

  font-weight: 500;
}

.selected {
  background-color: var(--primary-light);
}

.statusLabel {
  display:flex;
  flex-flow: row;
  gap: 8px;
}

.statusCount {
  display: flex;
  align-items: center;
  justify-content: center;

  width: 32px;
  height: 24px;
  border: 1px solid $gray700;
  border-radius: 100px;

  font-size: 12px;
  font-weight: 600;
}

.inquiriesBar {
  display: flex;
  flex: 3;
  flex-flow: column;
  background-color: $gray50;
}

.inquiries {
  padding-right: 8px;
}

.filtersBar {
  display:flex;
  flex-flow: row;
  gap: 16px;

  padding: 16px;
  border-radius: 0;
}

.selectFilter {
  border: 1px solid $gray300;
  border-radius: 6px;
}

.inputFilter {
  max-width: unset;
  height: 36px;
  margin: 0;
  padding: 0;
  border: 1px solid $gray300;
  border-radius: 6px;

  background-color: $gray50;
}
