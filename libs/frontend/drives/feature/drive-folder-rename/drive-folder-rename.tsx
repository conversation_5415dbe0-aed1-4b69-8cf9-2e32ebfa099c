import './drive-folder-rename.scss';
import { DrivesClient } from '@mynotary/frontend/drives/core';
import { selectDriveFolder, selectDrivesFeature, updateDriveFolder } from '@mynotary/frontend/drives/store';
import { MnRenamePopin } from '@mynotary/frontend/files/api';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { ActionIcon } from '@mynotary/frontend/shared/ui';
import { incrementDuplicateLabels } from '@mynotary/frontend/shared/util';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

interface DriveFolderRenameProps {
  folderId: string;
  onFinish?: () => void;
}

export const DriveFolderRename = ({ folderId, onFinish }: DriveFolderRenameProps) => {
  const [open, setOpen] = useState(false);

  const currentFolder = useSelector(selectDriveFolder(folderId));
  const folders = useSelector(selectDrivesFeature)?.folders ?? [];

  const dispatch = useDispatch();

  const drivesClient = useService(DrivesClient);

  const handleRenameFolder = async (label: string) => {
    try {
      const filteredFolders = folders.filter((folder) => folder.id !== folderId);
      const updatedLabel = incrementDuplicateLabels({
        existingLabels: filteredFolders.map((folder) => folder.label),
        targetLabel: label
      });

      await drivesClient.updateDriveFolder({ id: folderId, label: updatedLabel });
      dispatch(updateDriveFolder({ id: folderId, label: updatedLabel }));
    } finally {
      close();
    }
  };

  const close = () => {
    setOpen(false);
    onFinish?.();
  };

  const handleClickAction = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    setOpen(true);
  };

  return (
    <div onClick={(e) => e.stopPropagation()}>
      <ActionIcon
        icon={'/assets/images/pictos/icon/user-header.svg'}
        label={'Renommer le dossier'}
        onClick={handleClickAction}
      />

      <MnRenamePopin
        defaultValue={currentFolder?.label}
        editable={true}
        isOpen={open}
        onClose={() => setOpen(false)}
        onValidate={handleRenameFolder}
        title='Renommer un sous-dossier'
      />
    </div>
  );
};
