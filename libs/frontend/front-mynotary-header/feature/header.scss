@use 'style/variables/colors' as *;
@use 'style/mixins/index' as *;
@use 'style/variables/index' as *;

.mn-header {
  @include responsive($tablet-max) {
    height: $header-height-responsive;
  }

  position: fixed;
  z-index: $z-index-header;
  top: 0;

  display: flex;
  align-items: center;

  width: 100%;
  height: $header-height;
  padding: 0 24px;
  border-bottom: 1px solid $gray150;

  background: white;

  &.overflow {
    .header-vertical {
      display: flex;
    }

    .header-horizontal {
      position: absolute;
      visibility: hidden;
    }
  }
}
