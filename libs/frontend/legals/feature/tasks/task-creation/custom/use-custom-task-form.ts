import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { computeVisibleProgression, filterForm, mergeAndCopyAnswer } from '@mynotary/frontend/legals/core';
import { TaskType } from '@mynotary/crossplatform/legals/core';
import { User } from '@mynotary/frontend/shared/util';
import { useState } from 'react';
import { useTaskAssignees } from '../use-task-assignees';
import { getTaskDefaultAnswer, taskAssignees, taskDeadline, taskDescription, taskShare, taskTitle } from '../task-form';
import { map } from 'lodash';
import {
  DateFormQuestion,
  EmailFormQuestion,
  FormQuestion,
  SelectFormQuestion
} from '@mynotary/crossplatform/shared/forms-util';

interface UseTaskForm {
  operationId: number;
}

export const useCustomTaskForm = ({ operationId }: UseTaskForm) => {
  const [answer, setAnswer] = useState<AnswerDict>(getTaskDefaultAnswer({ type: TaskType.CUSTOM }));
  const { assignees, isAssigneesLoading } = useTaskAssignees({
    operationId,
    taskType: TaskType.CUSTOM
  });

  const form = getCustomTaskForm({ answer, assignees, isAssigneesLoading });

  const handleAnswerChange = (update: AnswerDict): void => {
    const fullAnswer = mergeAndCopyAnswer(answer, update);
    setAnswer(fullAnswer);
  };

  const { mandatoryFilled, mandatoryTotal } = computeVisibleProgression(form, answer);
  const isCompleted = mandatoryFilled === mandatoryTotal;

  return {
    answer,
    form,
    handleAnswerChange,
    isCompleted
  };
};

const getCustomTaskForm = (args: { answer: AnswerDict; assignees: User[]; isAssigneesLoading: boolean }) => {
  if (args.isAssigneesLoading) {
    return [];
  }
  const assigneeQuestion: EmailFormQuestion = {
    ...taskAssignees({ multiple: true }),
    fetchContacts: true,
    options: map(args.assignees, (assigne) => ({
      email: assigne.email,
      id: assigne.id,
      nom: `${assigne.lastname}`,
      prenoms: `${assigne.firstname}`
    }))
  };

  const form: Array<FormQuestion | SelectFormQuestion | DateFormQuestion | EmailFormQuestion> = [
    taskTitle,
    taskDescription,
    ...taskDeadline,
    taskShare({ externalLink: false, selfAssignment: true }),
    assigneeQuestion
  ];
  return filterForm(form, args.answer, { CONDITION: {} });
};
