import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AnnexedDocument } from '@mynotary/frontend/legals/core';

interface AnnexedDocuments {
  annexedDocuments: AnnexedDocument[];
}

const initialState: AnnexedDocuments = {
  annexedDocuments: []
};

export const annexedDocumentsSlice = createSlice({
  initialState,
  name: 'annexedDocuments',
  reducers: {
    addAnnexedDocument: (state, action: PayloadAction<AnnexedDocument>) => {
      const existingDocumentIndex = state.annexedDocuments.findIndex((doc) => doc.id === action.payload.id);

      if (existingDocumentIndex !== -1) {
        state.annexedDocuments[existingDocumentIndex] = action.payload;
      } else {
        state.annexedDocuments = [...state.annexedDocuments, action.payload];
      }
    },
    setAnnexedDocuments: (state, action: PayloadAction<AnnexedDocument[]>) => {
      state.annexedDocuments = action.payload;
    }
  }
});

export const { addAnnexedDocument, setAnnexedDocuments } = annexedDocumentsSlice.actions;

export interface AnnexedDocumentsState {
  [annexedDocumentsSlice.name]: AnnexedDocuments;
}

export const selectAnnexedDocumentsFeature = (state: AnnexedDocumentsState) => state[annexedDocumentsSlice.name];
