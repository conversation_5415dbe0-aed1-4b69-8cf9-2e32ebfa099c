import {
  getDefaultBranches,
  isContractBranch,
  NewMnBranch
} from '@mynotary/frontend/legals/core';
import { memoize } from 'lodash';
import { createSelector } from '@reduxjs/toolkit';
import { selectLegalComponentTemplate } from '../templates/select-legal-component-template';
import { selectContract } from '../contracts/contracts.selector';
import { LegalLinkTemplate, LegalTemplate } from '@mynotary/crossplatform/legal-templates/api'

export const selectDefaultBranch = memoize(
  (fromId: number, linkId: number, contractId?: number) =>
    createSelector(
      selectLegalComponentTemplate<LegalTemplate>(fromId),
      selectLegalComponentTemplate<LegalLinkTemplate>(linkId),
      selectContract(contractId ?? -1),
      (fromTemplate, linkTemplate, contract): NewMnBranch | undefined => {
        if (fromTemplate && linkTemplate) {
          const defaultBranch = getDefaultBranches(fromId, fromTemplate, linkTemplate);

          if (defaultBranch) {
            if (contract && isContractBranch(defaultBranch.type, linkTemplate, contract)) {
              defaultBranch.specificContractId = contractId;
            }
            return defaultBranch;
          }
        }
        return undefined;
      }
    ),
  (...args) => args.join('')
);
