import { TaskNotificationType, TaskNotification } from '@mynotary/crossplatform/notifications/core';
import { fns } from '@mynotary/crossplatform/shared/dates-util';
import { NotificationReceivers } from './notification-receivers';
import { useNavigate } from 'react-router';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { updateNotification } from '@mynotary/frontend/notifications/store';
import { getOperationUrl, routePaths } from '@mynotary/frontend/routes/api';
import { formatUrlParam } from '@mynotary/frontend/shared/util';
import { QUERY_PARAMS_OPEN_TASKS } from '@mynotary/frontend/shared/static-data-util';
import { NotificationTile } from '@mynotary/frontend/notifications/ui';

interface NotificationTileTaskProps {
  notification: TaskNotification;
  onClick: () => void;
}

export const NotificationTileTask = ({ notification, onClick }: NotificationTileTaskProps) => {
  const dispatch = useAsyncDispatch();
  const navigate = useNavigate();

  const handleClick = () => {
    const operationId = parseInt(notification.data.operationId);
    const parentOperationId = notification.data.parentOperationId
      ? parseInt(notification.data.parentOperationId)
      : undefined;
    switch (notification.type) {
      case TaskNotificationType.TASK_VALIDATE_CONTRACT_COMPLETED:
      case TaskNotificationType.TASK_VALIDATE_CONTRACT_CREATED:
      case TaskNotificationType.TASK_VALIDATE_CONTRACT_CANCELED:
        navigate({
          pathname: getOperationUrl(
            {
              id: operationId,
              parentOperationId
            },
            routePaths.operation.contrats.redactionFollowUp.path,
            {
              contractId: notification.data.contractId,
              id: notification.data.operationId
            }
          )
        });
        break;
      default:
        navigate({
          pathname: getOperationUrl({ id: operationId, parentOperationId }, routePaths.operation.contrats.path, {
            id: notification.data.operationId
          }),
          search: formatUrlParam(`${QUERY_PARAMS_OPEN_TASKS}=true`)
        });
        break;
    }
    if (!notification.seen) {
      handleSeen();
    }
    onClick();
  };

  const handleSeen = () => {
    dispatch(updateNotification({ id: notification.id, seen: !notification.seen }));
  };

  return (
    <NotificationTile.Wrapper key={notification.id} onClick={handleClick}>
      <NotificationTile.Seen isSeen={notification.seen} onClickSeen={handleSeen} />
      <NotificationTile.Container>
        <NotificationTile.Header
          creationDate={notification?.creationDate}
          icon={getImagePath(notification.type)}
          label={getLabel(notification)}
        />
        <NotificationTile.List>
          {getStatus(notification.type)}
          <NotificationReceivers receivers={notification.data.receivers} />
          <NotificationTile.Label>
            Créée par : {`${notification.data.creatorFirstname} ${notification.data.creatorLastname}`}
          </NotificationTile.Label>
          {notification.data.expirationDate && (
            <div>Date d'expiration : {fns.format(new Date(notification.data.expirationDate), 'dd/MM/yyyy')}</div>
          )}
          <NotificationTile.Label>Contrat : {notification.data.contractLabel}</NotificationTile.Label>
          <NotificationTile.Label>Dossier : {notification.data.operationLabel}</NotificationTile.Label>
        </NotificationTile.List>
      </NotificationTile.Container>
    </NotificationTile.Wrapper>
  );
};

const getLabel = (notification: TaskNotification) => {
  switch (notification.type) {
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_COMPLETED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_CREATED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_EXPIRED:
      return `Demande de documents`;
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_REMINDER:
      return `Rappel: Demande de documents`;
    case TaskNotificationType.TASK_CUSTOM_COMPLETED:
    case TaskNotificationType.TASK_CUSTOM_CREATED:
    case TaskNotificationType.TASK_CUSTOM_EXPIRED:
      return notification.data.title;
    case TaskNotificationType.TASK_CUSTOM_REMINDER:
      return `Rappel: ${notification.data.title}`;
    case TaskNotificationType.TASK_DOWNLOAD_FILES_COMPLETED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_CREATED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_EXPIRED:
      return 'Document(s) à consulter';
    case TaskNotificationType.TASK_DOWNLOAD_FILES_REMINDER:
      return 'Rappel: Document(s) à consulter';
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_EXPIRED:
      return 'Document(s) à fournir';
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_REMINDER:
      return 'Rappel: Document(s) à fournir';
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_EXPIRED:
      return 'Informations à renseigner';
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_REMINDER:
      return 'Rappel: Informations à renseigner';
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_COMPLETED:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CREATED:
      return 'Contrat en attente de validation';
    case TaskNotificationType.TASK_REVIEW_CONTRACT_COMPLETED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_CREATED:
      return 'Contrat en attende de relecture';
    case TaskNotificationType.TASK_REVIEW_CONTRACT_CANCELED:
      return 'Annulation de la demande de relecture';
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_COMPLETED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_CREATED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_EXPIRED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_REMINDER:
      return 'Contrat à lire';
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CANCELED:
      return 'Annulation de la demande de validation';
    case TaskNotificationType.TASK_REVIEW_CONTRACT_COMPLETED:
      return 'Relecture du contrat confirmée';
  }
};

const getStatus = (type: TaskNotificationType) => {
  switch (type) {
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_COMPLETED:
    case TaskNotificationType.TASK_CUSTOM_COMPLETED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_COMPLETED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_COMPLETED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_COMPLETED:
      return <NotificationTile.Status color='success' label='Terminé' />;
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_CREATED:
    case TaskNotificationType.TASK_CUSTOM_CREATED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_CREATED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CREATED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_CREATED:
      return <NotificationTile.Status color='info' label='À faire' />;
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_EXPIRED:
    case TaskNotificationType.TASK_CUSTOM_EXPIRED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_EXPIRED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_EXPIRED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_EXPIRED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_EXPIRED:
      return <NotificationTile.Status color='error' label='Expiré' />;
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_REMINDER:
    case TaskNotificationType.TASK_CUSTOM_REMINDER:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_REMINDER:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_REMINDER:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_REMINDER:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_REMINDER:
      return <NotificationTile.Status color='error' label='En retard' />;
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CANCELED:
      return <NotificationTile.Status color='info' label='Demande annulée' />;
  }
};

const getImagePath = (type: TaskNotificationType) => {
  switch (type) {
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_COMPLETED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_CREATED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_EXPIRED:
    case TaskNotificationType.TASK_DOCUMENT_REQUEST_REMINDER:
      return `/assets/images/pictos/icon/mail-coming-light.svg`;
    case TaskNotificationType.TASK_CUSTOM_COMPLETED:
    case TaskNotificationType.TASK_CUSTOM_CREATED:
    case TaskNotificationType.TASK_CUSTOM_EXPIRED:
    case TaskNotificationType.TASK_CUSTOM_REMINDER:
      return `/assets/images/pictos/icon/task.svg`;
    case TaskNotificationType.TASK_DOWNLOAD_FILES_COMPLETED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_CREATED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_EXPIRED:
    case TaskNotificationType.TASK_DOWNLOAD_FILES_REMINDER:
      return `/assets/images/pictos/icon/send.svg`;
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_EXPIRED:
    case TaskNotificationType.TASK_FILL_OPERATION_RECORDS_REMINDER:
      return `/assets/images/pictos/icon/mail-coming-light.svg`;
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_COMPLETED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_CREATED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_EXPIRED:
    case TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_REMINDER:
      return `/assets/images/pictos/icon/infos-coordonnes-light.svg`;
    case TaskNotificationType.TASK_REVIEW_CONTRACT_COMPLETED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_CREATED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_CANCELED:
      return `/assets/images/pictos/icon/file-text.svg`;
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_COMPLETED:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CREATED:
    case TaskNotificationType.TASK_VALIDATE_CONTRACT_CANCELED:
    case TaskNotificationType.TASK_REVIEW_CONTRACT_COMPLETED:
      return `/assets/images/pictos/icon/contrat-validation.svg`;
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_COMPLETED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_CREATED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_EXPIRED:
    case TaskNotificationType.TASK_READ_CONTRACT_PROJECT_REMINDER:
      return `/assets/images/pictos/icon/send.svg`;
  }
};
