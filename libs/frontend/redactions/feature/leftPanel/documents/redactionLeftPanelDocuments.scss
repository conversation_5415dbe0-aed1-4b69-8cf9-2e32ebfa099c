@use 'style/variables/colors' as *;
@use 'style/mixins' as *;

.redaction-left-panel-documents {
  @include responsive($mobile-max) {
    margin-bottom: 60px;
  }

  @include responsive($tablet-max) {
    margin-bottom: 60px;
  }

  @include responsive($small-desktop-min) {
    margin-top: 0;
  }

  margin-bottom: 48px;

  .rlpd-content {
    overflow: auto;
    flex-grow: 1;
    height: 100%;
    background-color: $white;
  }

  .rlpd-record-group-list-empty {
    display: flex;
    flex-direction: column;
    align-items: center;

    width: 100%;
    margin: auto;

    > img {
      width: 170px;
      height: 170px;
      margin: 30px 0;
    }
  }

  .rlpd-record-group-container {
    margin-top: 32px;
  }

  .rlpd-record-group {
    margin-bottom: 32px;
  }

  .rlpd-title-selection {
    .mn-dts-label-wrapper {
      width: initial;
      max-width: 180px;
    }

    .mn-dts-text {
      @include small-font($semi-bold);
      @include multi-line-ellipsis(2);
    }
  }

  .rlpd-summary-mobile {
    display: flex;
    gap: 16px;
    align-items: center;
    justify-content: center;

    height: 48px;
    margin: 8px 40px;
  }

  .rlpd-summary-container {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .rlpd-summary-mobile-label {
    @include mn-ellipsis(320px);
    @include large-font($bold);

    color: $black;
  }
}
