
-- duplicate an answer value in another answer id
-- with operation specific type and record
-- specific types example : {CDC,IMMOBILIER,PROGRAMME}
-- lcb.type example : CONDITIONS_GENERALES

UPDATE answer
SET answer = answer::jsonb || json_build_object('new_answer_id', json_build_object('value', answer->'old_answer_id'->>'value'))::jsonb
WHERE answer->>'old_answer_id' IS NOT null
and answer->>'new_answer_id' IS null
AND id IN (
    SELECT
        DISTINCT a.id
    FROM legal_component_template lct
            INNER JOIN legal_component lc ON lct.id = lc.template_id
            INNER JOIN legal_component_branch lcb ON lc.id = lcb.from_id
            INNER JOIN legal_component_record lcr ON lcb.to_id = lcr.legal_component_id
            INNER JOIN answer a ON a.id = lcr.answer_id
    WHERE specific_types = '{SPECIFIC,TYPE}'
    AND lcb.type = 'BRANCH'
    AND to_id IS NOT NULL
    );